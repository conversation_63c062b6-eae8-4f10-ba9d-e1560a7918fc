/**
 * Centralized Date/Time Utility
 *
 * This utility provides standardised functions for date/time operations
 * throughout the application, with a focus on Australian format and timezone.
 */

import {
  addDays,
  addMonths,
  differenceInDays,
  differenceInMonths,
  endOfDay,
  endOfMonth,
  format,
  formatDistance,
  isAfter,
  isBefore,
  isSameDay,
  isSameMonth,
  isToday,
  isValid,
  parse,
  parseISO,
  startOfDay,
  startOfMonth,
  subDays,
  subMonths,
} from 'date-fns';
import { z } from 'zod/v4';

// Constants
export const DEFAULT_TIMEZONE = 'Australia/Sydney';
export const DATE_FORMAT = 'dd/MM/yyyy'; // Australian format
export const TIME_FORMAT = 'h:mm a';
export const DATETIME_FORMAT = 'dd/MM/yyyy h:mm a';
export const DATETIME_SECONDS_FORMAT = 'dd/MM/yyyy h:mm:ss a';
export const FILENAME_FORMAT = 'yyyyMMdd_HHmmss';
export const ISO_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSSxxx";
export const SESSION_EXPIRY_HOURS = 8;
export const AUDIT_LOG_RETENTION_DAYS = 7;

// Timezone Functions
/**
 * Get current time in Sydney timezone
 */
export function getCurrentSydneyTime(): Date {
  return new Date(
    new Date().toLocaleString('en-US', { timeZone: DEFAULT_TIMEZONE })
  );
}

/**
 * Convert any date to Sydney timezone
 */
export function toSydneyTime(date: Date | string | null): Date | null {
  if (!date) {
    return null;
  }
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return new Date(
    dateObj.toLocaleString('en-US', { timeZone: DEFAULT_TIMEZONE })
  );
}

// Formatting Functions
/**
 * Format a date with a default or custom format
 */
export function formatDate(
  date: Date | string | null,
  formatStr: string = DATE_FORMAT
): string {
  if (!date) {
    return '';
  }
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    if (!isValid(dateObj)) {
      return '';
    }
    return format(dateObj, formatStr);
  } catch (error) {
    console.error('Error formatting date:', error);
    return '';
  }
}

/**
 * Format a date for UI display (localised to Australian English)
 */
export function formatForDisplay(
  date: Date | string | null,
  includeTime = false
): string {
  if (!date) {
    return '';
  }
  return formatDate(date, includeTime ? DATETIME_FORMAT : DATE_FORMAT);
}

/**
 * Format a date with time for display
 */
export function formatWithTime(
  date: Date | string | null,
  includeSeconds = false
): string {
  if (!date) {
    return '';
  }
  return formatDate(
    date,
    includeSeconds ? DATETIME_SECONDS_FORMAT : DATETIME_FORMAT
  );
}

/**
 * Format a date for use in filenames
 */
export function formatForFilename(date: Date | string = new Date()): string {
  return formatDate(date, FILENAME_FORMAT);
}

/**
 * Format a date for database operations (ISO format)
 */
export function formatForDatabase(date: Date | string | null): string {
  if (!date) {
    return '';
  }
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toISOString();
}

/**
 * Format a date for database operations while preserving the local date
 * This prevents timezone shifts that can cause off-by-one date errors
 * Returns YYYY-MM-DD format without timezone conversion
 */
export function formatDateForDatabase(date: Date | string | null): string {
  if (!date) {
    return '';
  }

  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    if (!isValid(dateObj)) {
      return '';
    }

    // Format as YYYY-MM-DD using local date components to avoid timezone issues
    // This ensures the date stays exactly as the user selected it
    const year = dateObj.getFullYear();
    const month = String(dateObj.getMonth() + 1).padStart(2, '0');
    const day = String(dateObj.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  } catch (error) {
    console.error('Error formatting date for database:', error);
    return '';
  }
}

/**
 * Format a date in Sydney timezone with Australian locale
 */
export function formatInSydneyTime(
  date: Date | string | null,
  options: Intl.DateTimeFormatOptions = {
    dateStyle: 'medium',
    timeStyle: 'short',
  }
): string {
  if (!date) {
    return '';
  }
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    if (!isValid(dateObj)) {
      return '';
    }
    return dateObj.toLocaleString('en-AU', {
      ...options,
      timeZone: DEFAULT_TIMEZONE,
    });
  } catch (error) {
    console.error('Error formatting date in Sydney timezone:', error);
    return '';
  }
}

// Parsing Functions
/**
 * Safely parse a date string to a Date object
 */
export function parseDate(dateString: string | null): Date | null {
  if (!dateString) {
    return null;
  }
  try {
    // Try parsing as ISO format first
    const date = parseISO(dateString);
    if (isValid(date)) {
      return date;
    }

    // Try parsing as Australian format
    return parse(dateString, DATE_FORMAT, new Date());
  } catch (error) {
    console.error('Error parsing date:', error);
    return null;
  }
}

/**
 * Parse a date from database format (ISO string)
 */
export function parseDatabaseDate(dateString: string | null): Date | null {
  if (!dateString) {
    return null;
  }
  try {
    const date = parseISO(dateString);
    return isValid(date) ? date : null;
  } catch (error) {
    console.error('Error parsing database date:', error);
    return null;
  }
}

// Validation Helpers
/**
 * Check if a value is a valid date
 */
export function isValidDate(date: unknown): boolean {
  if (!date) {
    return false;
  }
  const dateObj =
    typeof date === 'string'
      ? new Date(date)
      : date instanceof Date
        ? date
        : new Date(String(date));
  return dateObj instanceof Date && !Number.isNaN(dateObj.getTime());
}

/**
 * Check if date1 is after date2
 */
export function isDateAfter(
  date1: Date | string | null,
  date2: Date | string | null
): boolean {
  if (!(date1 && date2)) {
    return false;
  }
  const d1 = typeof date1 === 'string' ? new Date(date1) : date1;
  const d2 = typeof date2 === 'string' ? new Date(date2) : date2;
  return isAfter(d1, d2);
}

/**
 * Check if date1 is before date2
 */
export function isDateBefore(
  date1: Date | string | null,
  date2: Date | string | null
): boolean {
  if (!(date1 && date2)) {
    return false;
  }
  const d1 = typeof date1 === 'string' ? new Date(date1) : date1;
  const d2 = typeof date2 === 'string' ? new Date(date2) : date2;
  return isBefore(d1, d2);
}

/**
 * Check if dates are the same day
 */
export function isSameDate(
  date1: Date | string | null,
  date2: Date | string | null
): boolean {
  if (!(date1 && date2)) {
    return false;
  }
  const d1 = typeof date1 === 'string' ? new Date(date1) : date1;
  const d2 = typeof date2 === 'string' ? new Date(date2) : date2;
  return isSameDay(d1, d2);
}

/**
 * Check if dates are in the same month
 */
export function isSameMonthDate(
  date1: Date | string | null,
  date2: Date | string | null
): boolean {
  if (!(date1 && date2)) {
    return false;
  }
  const d1 = typeof date1 === 'string' ? new Date(date1) : date1;
  const d2 = typeof date2 === 'string' ? new Date(date2) : date2;
  return isSameMonth(d1, d2);
}

// Calculation Helpers
export {
  addDays,
  subDays,
  addMonths,
  subMonths,
  differenceInDays,
  differenceInMonths,
  startOfDay,
  endOfDay,
  startOfMonth,
  endOfMonth,
  formatDistance,
  isSameMonth,
  isSameDay,
  isToday,
};

// Zod Integration
/**
 * Create a Zod schema for Date objects
 * Compatible with Zod v4
 */
export function createDateSchema(
  options: {
    required?: boolean;
    min?: Date;
    max?: Date;
    minMessage?: string;
    maxMessage?: string;
  } = {}
): z.ZodType<Date | null> {
  let schema = z.date();

  if (options.min) {
    const message =
      options.minMessage ||
      `Date must be after ${formatForDisplay(options.min)}`;
    schema = schema.min(options.min, { error: message });
  }

  if (options.max) {
    const message =
      options.maxMessage ||
      `Date must be before ${formatForDisplay(options.max)}`;
    schema = schema.max(options.max, { error: message });
  }

  return options.required ? schema : schema.nullable();
}

/**
 * Create a Zod schema for date strings
 * Compatible with Zod v4
 */
export function createDateStringSchema(
  options: {
    required?: boolean;
    min?: Date;
    max?: Date;
    format?: string;
    minMessage?: string;
    maxMessage?: string;
    formatMessage?: string;
  } = {}
): z.ZodType<string | null> {
  const formatStr = options.format || DATE_FORMAT;
  const formatMessage =
    options.formatMessage || `Date must be in format: ${formatStr}`;

  // Create base schema
  const baseSchema = z.string();

  // Create a schema with all validations
  const fullSchema = baseSchema.superRefine((val, ctx) => {
    // Handle empty values
    if (!val) {
      if (options.required) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Date is required',
        });
      }
      return;
    }

    // Validate format
    let date: Date;
    try {
      date = parse(val, formatStr, new Date());
      if (!isValid(date)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: formatMessage,
        });
        return;
      }
    } catch {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: formatMessage,
      });
      return;
    }

    // Validate min date
    if (options.min && !isAfter(date, options.min)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message:
          options.minMessage ||
          `Date must be after ${formatForDisplay(options.min)}`,
      });
    }

    // Validate max date
    if (options.max && isAfter(date, options.max)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message:
          options.maxMessage ||
          `Date must be before ${formatForDisplay(options.max)}`,
      });
    }
  });

  return options.required ? fullSchema : fullSchema.nullable();
}

/**
 * Create a refinement function for date range validation
 */
export function dateRangeRefinement<T extends Record<string, unknown>>(
  startField: keyof T,
  endField: keyof T,
  message = 'End date must be after start date',
  path: keyof T = endField as keyof T
) {
  return {
    message,
    path: [path],
    validation: (data: T) => {
      if (!(data[startField] && data[endField])) {
        return true;
      }
      return isDateAfter(
        data[endField] as Date | string,
        data[startField] as Date | string
      );
    },
  };
}

// Future Date Validation Utilities
/**
 * Get today's date in Sydney timezone (start of day)
 * Used for maximum date validation to prevent future dates
 */
export function getTodayInSydney(): Date {
  const sydneyTime = getCurrentSydneyTime();
  return startOfDay(sydneyTime);
}

/**
 * Create a Zod schema for Date objects that prevents future dates
 * Uses Sydney timezone for "today" calculation
 */
export function createDateOfBirthSchema(
  required = true
): z.ZodType<Date | null> {
  const today = getTodayInSydney();
  return createDateSchema({
    required,
    max: today,
    maxMessage: 'Date cannot be in the future',
  });
}

/**
 * Create a Zod schema for sacrament dates that prevents future dates
 * Uses Sydney timezone for "today" calculation
 */
export function createSacramentDateSchema(
  required = false
): z.ZodType<Date | null> {
  const today = getTodayInSydney();
  return createDateSchema({
    required,
    max: today,
    maxMessage: 'Date cannot be in the future',
  });
}

/**
 * Create a Zod schema for date strings that prevents future dates
 * Uses Sydney timezone for "today" calculation
 */
export function createDateOfBirthStringSchema(
  required = true
): z.ZodType<string | null> {
  const today = getTodayInSydney();
  return createDateStringSchema({
    required,
    max: today,
    maxMessage: 'Date cannot be in the future',
  });
}

/**
 * Create a Zod schema for sacrament date strings that prevents future dates
 * Uses Sydney timezone for "today" calculation
 */
export function createSacramentDateStringSchema(
  required = false
): z.ZodType<string | null> {
  const today = getTodayInSydney();
  return createDateStringSchema({
    required,
    max: today,
    maxMessage: 'Date cannot be in the future',
  });
}
