import { prisma } from '@/lib/db/prisma';
// formatForFilename is used in commented code for future implementation
import { formatForDatabase } from '@/lib/utils/date-time';
import { exportDatabase } from './export-prisma';

// Types for import validation
export interface ImportValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  tables: string[];
  recordCount: number;
}

// Log import activity
export async function logImport(
  adminId: number,
  importType: string,
  success: boolean,
  _recordCount: number,
  details: Record<string, unknown>,
  ipAddress?: string
): Promise<void> {
  try {
    const parameters = JSON.stringify({
      importType,
      success,
      details,
      timestamp: formatForDatabase(new Date()),
    });

    await prisma.auditLog.create({
      data: {
        userType: 'admin',
        userId: adminId,
        action: success ? 'import-database-success' : 'import-database-failure',
        entityType: 'database',
        entityId: 0,
        ipAddress: ipAddress || null,
        newValues: parameters,
      },
    });
  } catch (error) {
    // Environment-aware logging - only in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Failed to log import activity:', error);
    }
    // Don't throw - this is a non-critical operation
  }
}

// Create a backup before import
export async function createPreImportBackup(adminId: number): Promise<string> {
  // Generate a backup of all tables in SQL format
  const backup = await exportDatabase({
    format: 'sql',
    tables: 'all',
    adminId,
  });

  // Generate a unique timestamp for this backup (commented out until file storage is implemented)
  // const timestamp = formatForFilename(new Date());
  // const filename = `pre_import_backup_${timestamp}.sql`; // Uncomment when implementing file storage

  // In a production environment, you would save this to a file or cloud storage
  // For now, we'll just return the SQL content
  return backup.data as string;
}

// Validate SQL import
export async function validateSQLImport(
  sql: string
): Promise<ImportValidationResult> {
  const result: ImportValidationResult = {
    isValid: true,
    errors: [],
    warnings: [],
    tables: [],
    recordCount: 0,
  };

  try {
    // Basic validation - check for SQL syntax
    if (!(sql.includes('CREATE TABLE') || sql.includes('INSERT INTO'))) {
      result.isValid = false;
      result.errors.push(
        'The SQL file does not contain valid CREATE TABLE or INSERT statements'
      );
      return result;
    }

    // Extract table names from the SQL
    const tableRegex = /CREATE TABLE\s+(?:`|")?([a-zA-Z0-9_]+)(?:`|")?/g;
    let match;
    while ((match = tableRegex.exec(sql)) !== null) {
      result.tables.push(match[1]);
    }

    // Count approximate number of records
    const insertRegex = /INSERT INTO/g;
    const insertMatches = sql.match(insertRegex);
    result.recordCount = insertMatches ? insertMatches.length : 0;

    // Check for potentially dangerous operations
    if (sql.includes('DROP DATABASE') || sql.includes('CREATE DATABASE')) {
      result.isValid = false;
      result.errors.push(
        'The SQL file contains database-level operations which are not allowed'
      );
    }

    // Check for system tables that shouldn't be modified
    const systemTables = ['user_sessions', 'audit_logs', 'export_logs'];
    for (const table of systemTables) {
      if (
        sql.includes(`DROP TABLE \`${table}\``) ||
        sql.includes(`CREATE TABLE \`${table}\``)
      ) {
        result.warnings.push(
          `The SQL file contains operations on system table '${table}'`
        );
      }
    }

    return result;
  } catch (error) {
    result.isValid = false;
    result.errors.push(
      `Error validating SQL: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
    return result;
  }
}

// Validate CSV import
export async function validateCSVImport(
  files: Record<string, string>
): Promise<ImportValidationResult> {
  const result: ImportValidationResult = {
    isValid: true,
    errors: [],
    warnings: [],
    tables: Object.keys(files),
    recordCount: 0,
  };

  try {
    // Check if we have at least one file
    if (Object.keys(files).length === 0) {
      result.isValid = false;
      result.errors.push('No CSV files found in the import package');
      return result;
    }

    // Validate each CSV file
    for (const [table, content] of Object.entries(files)) {
      // Check if the table exists in the database using Prisma introspection
      try {
        // Try to query the table to see if it exists
        await prisma.$queryRaw`SELECT 1 FROM ${table} LIMIT 1`;
      } catch {
        result.warnings.push(
          `Table '${table}' does not exist in the database and will be skipped`
        );
        continue;
      }

      // Check if the CSV has headers
      const lines = content.trim().split('\n');
      if (lines.length === 0) {
        result.warnings.push(`CSV for table '${table}' is empty`);
        continue;
      }

      // Get the headers from the first line
      const headers = lines[0]
        .split(',')
        .map((h) => h.trim().replace(/^["']|["']$/g, ''));

      // Validate headers against table columns using Prisma introspection
      // For PostgreSQL, we'll use a different approach to get column names
      const columnInfo = await prisma.$queryRaw<{ column_name: string }[]>`
        SELECT column_name FROM information_schema.columns
        WHERE table_schema = 'public' AND table_name = ${table}
      `;

      const columnNames = columnInfo.map((c) => c.column_name);
      const missingColumns = headers.filter((h) => !columnNames.includes(h));

      if (missingColumns.length > 0) {
        result.warnings.push(
          `CSV for table '${table}' contains unknown columns: ${missingColumns.join(', ')}`
        );
      }

      // Count records
      result.recordCount += lines.length - 1; // Subtract 1 for the header
    }

    return result;
  } catch (error) {
    result.isValid = false;
    result.errors.push(
      `Error validating CSV: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
    return result;
  }
}

// Validate JSON import
export async function validateJSONImport(
  json: Record<string, unknown[]>
): Promise<ImportValidationResult> {
  const result: ImportValidationResult = {
    isValid: true,
    errors: [],
    warnings: [],
    tables: Object.keys(json),
    recordCount: 0,
  };

  try {
    // Check if we have at least one table
    if (Object.keys(json).length === 0) {
      result.isValid = false;
      result.errors.push('No tables found in the JSON import');
      return result;
    }

    // Validate each table
    for (const [table, records] of Object.entries(json)) {
      // Check if the table exists in the database using Prisma
      try {
        await prisma.$queryRaw`SELECT 1 FROM ${table} LIMIT 1`;
      } catch {
        result.warnings.push(
          `Table '${table}' does not exist in the database and will be skipped`
        );
        continue;
      }

      // Check if records is an array
      if (!Array.isArray(records)) {
        result.isValid = false;
        result.errors.push(`Data for table '${table}' is not an array`);
        continue;
      }

      // If we have records, validate the first one against table columns
      if (records.length > 0) {
        const firstRecord = records[0];
        const columnInfo = await prisma.$queryRaw<{ column_name: string }[]>`
          SELECT column_name FROM information_schema.columns
          WHERE table_schema = 'public' AND table_name = ${table}
        `;

        const columnNames = columnInfo.map((c) => c.column_name);
        const recordKeys = Object.keys(firstRecord as object);
        const unknownColumns = recordKeys.filter(
          (k) => !columnNames.includes(k)
        );

        if (unknownColumns.length > 0) {
          result.warnings.push(
            `JSON for table '${table}' contains unknown columns: ${unknownColumns.join(', ')}`
          );
        }
      }

      // Count records
      result.recordCount += records.length;
    }

    return result;
  } catch (error) {
    result.isValid = false;
    result.errors.push(
      `Error validating JSON: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
    return result;
  }
}

// Import SQL data
export async function importSQL(
  sql: string
): Promise<{ success: boolean; message: string }> {
  try {
    // Split SQL into individual statements
    const statements = sql
      .replace(/\/\*[\s\S]*?\*\//g, '') // Remove comments
      .replace(/--.*?$/gm, '') // Remove single-line comments
      .split(';')
      .filter((stmt) => stmt.trim().length > 0);

    // Execute each statement in a transaction
    await prisma.$transaction(async (tx) => {
      for (const stmt of statements) {
        await tx.$executeRawUnsafe(stmt);
      }
    });

    return {
      success: true,
      message: `Successfully imported ${statements.length} SQL statements`,
    };
  } catch (error) {
    return {
      success: false,
      message: `Error importing SQL: ${error instanceof Error ? error.message : 'Unknown error'}`,
    };
  }
}

// Import CSV data
export async function importCSV(
  files: Record<string, string>
): Promise<{ success: boolean; message: string }> {
  try {
    const queries: {
      query: string;
      params: (string | number | boolean | null | undefined | Date)[];
    }[] = [];

    for (const [table, content] of Object.entries(files)) {
      // Check if the table exists
      try {
        await prisma.$queryRaw`SELECT 1 FROM ${table} LIMIT 1`;
      } catch {
        continue; // Skip tables that don't exist
      }

      // Parse CSV
      const lines = content.trim().split('\n');
      if (lines.length <= 1) {
        continue; // Skip empty files or files with only headers
      }

      // Get headers
      const headers = lines[0]
        .split(',')
        .map((h) => h.trim().replace(/^["']|["']$/g, ''));

      // Get valid columns for this table
      const columnInfo = await prisma.$queryRaw<{ column_name: string }[]>`
        SELECT column_name FROM information_schema.columns
        WHERE table_schema = 'public' AND table_name = ${table}
      `;

      const columnNames = columnInfo.map((c) => c.column_name);
      const validHeaders = headers.filter((h) => columnNames.includes(h));

      // Skip if no valid headers
      if (validHeaders.length === 0) {
        continue;
      }

      // Clear existing data
      queries.push({
        query: `DELETE FROM "${table}"`,
        params: [],
      });

      // Process each data row
      for (let i = 1; i < lines.length; i++) {
        const line = lines[i].trim();
        if (!line) {
          continue;
        }

        // Parse CSV line (simple implementation - doesn't handle all CSV edge cases)
        const values: string[] = [];
        let inQuote = false;
        let currentValue = '';

        for (let j = 0; j < line.length; j++) {
          const char = line[j];

          if (char === '"' && (j === 0 || line[j - 1] !== '\\')) {
            inQuote = !inQuote;
          } else if (char === ',' && !inQuote) {
            values.push(currentValue);
            currentValue = '';
          } else {
            currentValue += char;
          }
        }

        values.push(currentValue); // Add the last value

        // Create insert query
        const placeholders = validHeaders.map(() => '?').join(', ');
        const insertQuery = `INSERT INTO "${table}" ("${validHeaders.join('", "')}")VALUES (${placeholders})`;

        // Extract values for valid headers
        const rowValues = validHeaders.map((header) => {
          const index = headers.indexOf(header);
          let value =
            index >= 0 && index < values.length ? values[index] : null;

          // Clean up value
          if (value !== null) {
            value = value.trim().replace(/^["']|["']$/g, '');
            if (value === '') {
              value = null;
            }
          }

          return value;
        });

        queries.push({
          query: insertQuery,
          params: rowValues,
        });
      }
    }

    // Execute all queries in a transaction
    await prisma.$transaction(async (tx) => {
      for (const query of queries) {
        await tx.$executeRawUnsafe(query.query, ...query.params);
      }
    });

    return {
      success: true,
      message: `Successfully imported data for ${Object.keys(files).length} tables`,
    };
  } catch (error) {
    return {
      success: false,
      message: `Error importing CSV: ${error instanceof Error ? error.message : 'Unknown error'}`,
    };
  }
}

// Import JSON data
export async function importJSON(
  json: Record<string, unknown[]>
): Promise<{ success: boolean; message: string }> {
  try {
    const queries: {
      query: string;
      params: (string | number | boolean | null | undefined | Date)[];
    }[] = [];

    for (const [table, records] of Object.entries(json)) {
      // Check if the table exists
      try {
        await prisma.$queryRaw`SELECT 1 FROM ${table} LIMIT 1`;
      } catch {
        continue; // Skip tables that don't exist
      }

      if (!Array.isArray(records) || records.length === 0) {
        continue; // Skip tables with no records
      }

      // Get valid columns for this table
      const columnInfo = await prisma.$queryRaw<{ column_name: string }[]>`
        SELECT column_name FROM information_schema.columns
        WHERE table_schema = 'public' AND table_name = ${table}
      `;

      const columnNames = columnInfo.map((c) => c.column_name);

      // Clear existing data
      queries.push({
        query: `DELETE FROM "${table}"`,
        params: [],
      });

      // Process each record
      for (const record of records) {
        const recordKeys = Object.keys(record as object);
        const validKeys = recordKeys.filter((k) => columnNames.includes(k));

        // Skip if no valid keys
        if (validKeys.length === 0) {
          continue;
        }

        // Create insert query
        const placeholders = validKeys.map(() => '?').join(', ');
        const insertQuery = `INSERT INTO "${table}" ("${validKeys.join('", "')}")VALUES (${placeholders})`;

        // Extract values for valid keys
        const rawValues = validKeys.map(
          (key) => (record as Record<string, unknown>)[key]
        );

        // Convert unknown values to acceptable types
        const values = rawValues.map((val) => {
          if (
            val === null ||
            val === undefined ||
            typeof val === 'string' ||
            typeof val === 'number' ||
            typeof val === 'boolean'
          ) {
            return val;
          }
          // Convert other types to string
          return String(val);
        });

        queries.push({
          query: insertQuery,
          params: values,
        });
      }
    }

    // Execute all queries in a transaction
    await prisma.$transaction(async (tx) => {
      for (const query of queries) {
        await tx.$executeRawUnsafe(query.query, ...query.params);
      }
    });

    return {
      success: true,
      message: `Successfully imported data for ${Object.keys(json).length} tables`,
    };
  } catch (error) {
    return {
      success: false,
      message: `Error importing JSON: ${error instanceof Error ? error.message : 'Unknown error'}`,
    };
  }
}
