/**
 * Intent Analysis Module
 *
 * Contains AI-driven intent detection and analysis functions that process user messages
 * to determine the appropriate data type, analysis type, and confidence level.
 *
 * This module handles:
 * - User intent analysis using Google Gemini AI
 * - Sacrament type validation against database
 * - Prompt injection detection and security
 * - JSON response parsing with fallback methods
 * - Dynamic temperature optimization for intent analysis
 * - Comprehensive recognition patterns for all data types
 * - Advanced JSON parsing with multiple fallback methods
 */

import { createGoogleGenerativeAI } from '@ai-sdk/google';
import { streamText } from 'ai';
import type { NextRequest } from 'next/server';
// Configuration
import {
  ENABLE_DEBUG_LOGGING,
  GEMINI_API_KEY,
  GEMINI_MODEL_NAME,
} from '@/lib/analytics/chatbot/config';
import {
  detectPromptInjection,
  logSecureError,
} from '@/lib/analytics/chatbot/security';
import { queryIntentSchema } from '@/lib/analytics/chatbot/validation';
import { prisma } from '@/lib/db/prisma';
import type { QueryIntent } from '@/types/analytics';
import { determineOptimalTemperature } from './temperature';

// AI-driven intent analysis function
export async function analyzeUserIntent(
  userMessage: string,
  _request: NextRequest
): Promise<QueryIntent> {
  // Fast pre-validation: Check for prompt injection before AI analysis
  if (detectPromptInjection(userMessage)) {
    if (ENABLE_DEBUG_LOGGING) {
      console.warn(
        'Prompt injection detected in intent analysis:',
        userMessage.substring(0, 100)
      );
    }

    // Return low-confidence general intent to trigger proper error handling
    return {
      dataType: 'general',
      analysisType: 'overview',
      confidence: 0.0, // Zero confidence indicates security issue
      chartRequested: false,
    };
  }

  // Fetch current sacrament types from database for AI recognition
  const sacramentTypes = await prisma.sacramentType.findMany({
    select: { code: true, name: true },
    orderBy: { id: 'asc' },
  });

  const sacramentTypesList = sacramentTypes
    .map((st) => `${st.code} (${st.name})`)
    .join(', ');

  try {
    // 2025 AI-First Sacrament Recognition: Dynamic database integration
    const intentPrompt = `You are an intelligent intent classifier for a census analytics system. Analyze the user's input and determine their intent using your natural language understanding.

CORE PRINCIPLE: Understand that human communication varies widely - from formal data requests to casual greetings to conversational interactions. All forms of communication are valid and should be interpreted based on context and intent, not specific words or patterns.

For any input that appears to be a greeting, conversation starter, or general interaction (regardless of formality level), classify it as "general" intent with "overview" analysis type and assign high confidence (0.8-0.9) based on your understanding of the communicative intent.

For specific data requests, analyze the content to determine the appropriate data type, analysis type, and filters based on what the user is asking for.

AVAILABLE SACRAMENT TYPES: ${sacramentTypesList}

You MUST respond with ONLY a complete, valid JSON object. No explanations, markdown, or additional text.

Required JSON format (all fields required, use null for optional fields not mentioned):
{
  "dataType": "member_demographics|household_info|sacrament_records|census_participation|temporal_analysis|general",
  "analysisType": "count|distribution|list|chart|overview|specific",
  "filters": {
    "gender": "male" | "female" | "other" | null,
    "ageRange": {"min": number, "max": number} | null,
    "location": "suburb name" | null,
    "sacramentType": "baptism" | "confirmation" | "communion" | "matrimony" | null,
    "censusYear": number | null,
    "relationship": "head" | "spouse" | "child" | "parent" | "relative" | "other" | null,
    "hobby": "hobby name" | "all" | null,
    "occupation": "occupation name" | "all" | null
  },
  "chartRequested": true | false,
  "confidence": 0.0-1.0
}

IMPORTANT JSON REQUIREMENTS:
- Always include the "filters" object, even if all values are null
- Use exact string values from the enum options, not descriptions
- For distribution/chart requests, set "chartRequested": true
- For demographic queries, use "dataType": "member_demographics"
- For household queries, use "dataType": "household_info"
- For sacrament queries, use "dataType": "sacrament_records"
- For unique code queries, use "dataType": "census_participation"
- For list requests (names, details, individual records), use "analysisType": "list"
- For counting requests, use "analysisType": "count"
- For statistical breakdowns, use "analysisType": "distribution"

SACRAMENT RECOGNITION:
- If user mentions sacrament-related terms, match to available sacrament types above using the CODE
- Handle multilingual input (English/Chinese) naturally
- Examples: "first holy communion", "初领圣体" → communion; "baptism", "洗礼" → baptism
- Use exact codes from available types: ${sacramentTypes.map((st) => st.code).join(', ')}

HOBBY AND OCCUPATION RECOGNITION:
- For hobby queries: "hobbies", "interests", "hobby distribution", "who likes X", "members with hobby Y"
- For occupation queries: "jobs", "occupations", "work", "profession", "career", "employment"
- Use "dataType": "member_demographics" for hobby/occupation analysis
- Set appropriate filters: "hobby": "all" for general distribution or "hobby": "specific hobby"
- Set appropriate filters: "occupation": "all" for general distribution or "occupation": "specific job"
- Examples: "hobby distribution" → hobby: "all"; "show teachers" → occupation: "teacher"

RELATIONSHIP RECOGNITION:
- For relationship queries: "relationships", "family roles", "household heads", "spouses", "children"
- Use "dataType": "household_info" for relationship analysis
- Set appropriate filters: "relationship": "all" for general distribution or specific relationship type
- Examples: "relationship distribution" → relationship: "all"; "how many heads" → relationship: "head"

AGE ANALYSIS RECOGNITION:
- For age queries: "age", "average age", "age distribution", "age groups", "demographics"
- Use "dataType": "member_demographics" with "analysisType": "distribution"
- Examples: "average age", "age distribution", "age demographics"

TEMPORAL ANALYSIS RECOGNITION:
- For year-over-year queries: "trends", "growth", "year comparison", "historical", "over time"
- Use "dataType": "temporal_analysis" for census year comparisons
- Examples: "growth trends", "year over year", "historical analysis"

COMMUNITY FEEDBACK RECOGNITION:
- For feedback queries: "feedback", "comments", "community input", "household comments", "suggestions"
- Use "dataType": "household_info" for community feedback analysis
- Examples: "recent feedback", "community concerns", "household comments"

User query: "${userMessage}"

CRITICAL: Return ONLY the complete JSON object. No explanations, no markdown, no additional text.`;

    // Use Gemini for intent analysis with low temperature for consistency
    const google = createGoogleGenerativeAI({
      apiKey: GEMINI_API_KEY!,
    });

    // 2025 Enhancement: Dynamic temperature based on input characteristics
    const optimalTemperature = determineOptimalTemperature(userMessage);

    if (ENABLE_DEBUG_LOGGING) {
      console.log('Intent analysis temperature:', optimalTemperature);
    }

    const result = await streamText({
      model: google(GEMINI_MODEL_NAME),
      system: intentPrompt,
      messages: [{ role: 'user', content: userMessage }],
      temperature: optimalTemperature, // Dynamic temperature for better understanding
      maxTokens: 1000, // Increased token limit to prevent JSON truncation
      // 2025 Feature: Dynamic thinking budget for complex reasoning
      experimental_providerMetadata: {
        google: {
          thinkingBudget: -1, // Dynamic thinking - let AI decide
        },
      },
    });

    // Convert stream to text
    let intentText = '';
    for await (const chunk of result.textStream) {
      intentText += chunk;
    }

    // Parse JSON response with multiple fallback methods
    const cleanedText = intentText.trim().replace(/```json|```/g, '');
    let intentData;

    try {
      // Method 1: Direct JSON parsing
      intentData = JSON.parse(cleanedText);
    } catch (error1) {
      try {
        // Method 2: Extract JSON from mixed content
        const jsonMatch = cleanedText.match(
          /\{(?:[^{}]|{(?:[^{}]|{[^{}]*})*})*\}/
        );
        if (jsonMatch) {
          intentData = JSON.parse(jsonMatch[0]);
        } else {
          throw new Error('No JSON found in response');
        }
      } catch (_error2) {
        try {
          // Method 3: Extract from code blocks
          const codeBlockMatch = cleanedText.match(
            /```(?:json)?\s*(\{[\s\S]*?\})\s*```/
          );
          if (codeBlockMatch) {
            intentData = JSON.parse(codeBlockMatch[1]);
          } else {
            throw new Error('No valid JSON format found');
          }
        } catch (_error3) {
          try {
            // Method 4: Handle incomplete JSON by attempting to complete it
            let incompleteJson = cleanedText.trim();
            if (
              incompleteJson.startsWith('{') &&
              !incompleteJson.endsWith('}')
            ) {
              // Try to complete the JSON with reasonable defaults
              if (
                incompleteJson.includes('"dataType": "member_demographics"')
              ) {
                incompleteJson = `{
                  "dataType": "member_demographics",
                  "analysisType": "list",
                  "confidence": 0.8,
                  "chartRequested": false
                }`;
              } else if (incompleteJson.includes('"dataType"')) {
                // Extract dataType and create minimal valid JSON
                const dataTypeMatch = incompleteJson.match(
                  /"dataType":\s*"([^"]+)"/
                );
                const analysisTypeMatch = incompleteJson.match(
                  /"analysisType":\s*"([^"]+)"/
                );
                incompleteJson = `{
                  "dataType": "${dataTypeMatch?.[1] || 'general'}",
                  "analysisType": "${analysisTypeMatch?.[1] || 'overview'}",
                  "confidence": 0.7,
                  "chartRequested": false
                }`;
              } else {
                throw new Error('Cannot repair incomplete JSON');
              }
              intentData = JSON.parse(incompleteJson);
            } else {
              throw new Error('No valid JSON format found');
            }
          } catch (_error4) {
            // All parsing methods failed - log and fallback
            logSecureError('json_parsing', error1, {
              originalText: cleanedText.substring(0, 200),
              message: userMessage.substring(0, 100),
            });
            throw error1; // Throw original error for fallback handling
          }
        }
      }
    }

    // Validate with Zod schema
    let validatedIntent;
    try {
      validatedIntent = queryIntentSchema.parse(intentData);
    } catch (zodError: any) {
      if (ENABLE_DEBUG_LOGGING) {
        console.error('Intent validation error:', zodError);
      }
      throw zodError;
    }

    if (ENABLE_DEBUG_LOGGING) {
      console.log('Validated intent:', validatedIntent);
    }

    return validatedIntent;
  } catch (error) {
    // Fallback to general intent if analysis fails
    logSecureError('intent_analysis', error, {
      message: userMessage.substring(0, 100),
    });

    // 2025 Pure AI-Native: Minimal fallback logic without pattern matching
    // Only use basic characteristics that don't involve word/pattern recognition
    const messageLength = userMessage.trim().length;

    // Simple fallback confidence based purely on message length and basic characteristics
    let fallbackConfidence = 0.1; // Default low confidence for AI analysis failures

    // Very short messages are often legitimate (greetings, simple responses)
    if (messageLength <= 5) {
      fallbackConfidence = 0.6; // Medium confidence for very short inputs
    } else if (messageLength <= 15) {
      fallbackConfidence = 0.5; // Reasonable confidence for short inputs
    } else if (messageLength > 50) {
      fallbackConfidence = 0.4; // Lower confidence for longer inputs when AI fails
    }

    if (ENABLE_DEBUG_LOGGING) {
      console.log(
        'Intent analysis fallback with confidence:',
        fallbackConfidence
      );
    }

    return {
      dataType: 'general',
      analysisType: 'overview',
      confidence: fallbackConfidence,
      chartRequested: false,
    };
  }
}

// Validate AI-recognized sacrament type against database
export async function validateSacramentType(
  intent: QueryIntent
): Promise<void> {
  if (intent.filters?.sacramentType) {
    // Direct query - fast enough without caching
    const validSacramentType = await prisma.sacramentType.findFirst({
      where: {
        code: {
          equals: intent.filters.sacramentType,
          mode: 'insensitive',
        },
      },
    });

    if (!validSacramentType) {
      // Invalid sacrament type - clear the filter
      intent.filters.sacramentType = null;
      if (ENABLE_DEBUG_LOGGING) {
        console.log('Invalid sacrament type cleared from intent filters');
      }
    }
  }
}
