'use client';

import { Loader2, Search, SlidersHorizontal, X } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useCallback, useEffect, useRef, useState } from 'react';
import { EditHouseholdDialog } from '@/components/admin/household/edit-household-dialog';
import { HouseholdTable } from '@/components/admin/household/household-table';
import { ViewHouseholdDialog } from '@/components/admin/household/view-household-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectSeparator,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useMessage } from '@/hooks/useMessage';
import { useSearch } from '@/hooks/useSearch';
import type { IHouseholdWithDetails } from '@/lib/db/households';
import type { ICensusYear } from '@/types';

interface HouseholdClientProps {
  censusYears: ICensusYear[];
  initialHouseholds: HouseholdWithDetails[];
  initialTotal: number;
  initialPageSize: number;
}

// Use the database interface directly for type consistency
type HouseholdWithDetails = IHouseholdWithDetails;

interface PaginationState {
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
}

export function HouseholdClient({
  censusYears,
  initialHouseholds,
  initialTotal,
  initialPageSize,
}: HouseholdClientProps) {
  const { showSuccess, showError } = useMessage();
  const t = useTranslations('admin');
  const tCommon = useTranslations('common');
  const tAccessibility = useTranslations('accessibility');

  // State for household data and UI
  const [households, setHouseholds] =
    useState<HouseholdWithDetails[]>(initialHouseholds);
  const [loading, setLoading] = useState(false); // Start with false since we have initial data
  const [pagination, setPagination] = useState<PaginationState>({
    page: 1,
    pageSize: initialPageSize,
    total: initialTotal,
    totalPages: Math.ceil(initialTotal / initialPageSize),
  });

  // State for dialogues
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [selectedHousehold, setSelectedHousehold] =
    useState<HouseholdWithDetails | null>(null);

  // State for filters
  const [filterCensusYearId, setFilterCensusYearId] = useState<number | null>(
    null
  );
  const [sortBy, setSortBy] = useState<string>('id');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Get active census year
  const activeCensusYear = censusYears.find((year) => year.isActive) || null;

  // Use our custom search hook with debounce
  const {
    searchTerm,
    debouncedSearchTerm,
    isSearching,
    setSearchTerm,
    clearSearch,
  } = useSearch({
    debounceDelay: 300,
  });

  // Fetch households with pagination and filtering
  const fetchHouseholds = useCallback(async () => {
    try {
      setLoading(true);

      // Build query parameters
      const params = new URLSearchParams();
      if (debouncedSearchTerm) {
        params.append('searchTerm', debouncedSearchTerm);
      }
      if (filterCensusYearId !== null) {
        params.append('censusYearId', filterCensusYearId.toString());
      }
      params.append('page', pagination.page.toString());
      params.append('pageSize', pagination.pageSize.toString());
      params.append('sortBy', sortBy);
      params.append('sortOrder', sortOrder);

      // Fetch households from API with improved timeout handling
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 15_000); // 15 second timeout (increased from 10s)

      try {
        const response = await fetch(
          `/api/admin/households/search?${params.toString()}`,
          {
            signal: controller.signal,
            // Add cache control headers to prevent stale data
            headers: {
              'Cache-Control': 'no-cache, no-store, must-revalidate',
              Pragma: 'no-cache',
            },
          }
        );

        clearTimeout(timeoutId);

        if (!response.ok) {
          const errorData = await response
            .json()
            .catch(() => ({ error: `Server error: ${response.status}` }));
          throw new Error(
            errorData.error ||
              `Failed to fetch households: ${response.statusText}`
          );
        }

        const data = await response.json();

        // Validate response data structure
        if (
          !(
            data.households &&
            Array.isArray(data.households) &&
            data.pagination
          )
        ) {
          throw new Error('Invalid response format from server');
        }

        setHouseholds(data.households);
        setPagination(data.pagination);
      } catch (error) {
        const fetchError = error as Error;
        if (fetchError.name === 'AbortError') {
          throw new Error('requestTimeout');
        }
        throw fetchError;
      }
    } catch (error) {
      console.error('Error fetching households:', error);

      // Use centralized error handling instead of raw error messages
      if (error instanceof Error && error.message.includes('timed out')) {
        showError('requestTimeout');
      } else {
        showError('householdSearchFailed');
      }

      // Set empty state on error to prevent showing stale data
      setHouseholds([]);
      setPagination((prev) => ({ ...prev, total: 0, totalPages: 0 }));
    } finally {
      setLoading(false);
    }
  }, [
    debouncedSearchTerm,
    filterCensusYearId,
    pagination.page,
    pagination.pageSize,
    sortBy,
    sortOrder,
    showError,
  ]); // showError is stable due to useCallback memoization

  // Initial fetch
  useEffect(() => {
    fetchHouseholds();
  }, [fetchHouseholds]);

  // Utility function to reset body styles and ensure interactivity
  const resetBodyStyles = useCallback(() => {
    // Force pointer-events to be enabled
    document.body.style.pointerEvents = 'auto';
    // Remove any overflow hidden that might have been added
    document.body.style.overflow = '';
    // Remove any padding right that might have been added to compensate for scrollbar
    document.body.style.paddingRight = '';
  }, []);

  // Ensure body is interactive when dialogues are closed
  useEffect(() => {
    // If dialogues are closed, reset body styles
    if (!(isViewDialogOpen || isEditDialogOpen)) {
      // Force pointer-events to be enabled
      document.body.style.pointerEvents = 'auto';
      // Remove any overflow hidden that might have been added
      document.body.style.overflow = '';
      // Remove any padding right that might have been added to compensate for scrollbar
      document.body.style.paddingRight = '';
    }

    // Cleanup function to ensure proper cleanup when component unmounts
    return () => {
      // Force pointer-events to be enabled
      document.body.style.pointerEvents = 'auto';
      // Remove any overflow hidden that might have been added
      document.body.style.overflow = '';
      // Remove any padding right that might have been added to compensate for scrollbar
      document.body.style.paddingRight = '';
    };
  }, [isViewDialogOpen, isEditDialogOpen]);

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    // Reset to first page when search changes
    setPagination((prev) => ({ ...prev, page: 1 }));
  };

  // Handle keyboard shortcut for search
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Focus search input when "/" is pressed
      if (e.key === '/' && document.activeElement !== searchInputRef.current) {
        e.preventDefault();
        searchInputRef.current?.focus();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Handle census year filter change
  const handleCensusYearFilterChange = (value: string) => {
    const yearId = value === 'all' ? null : Number.parseInt(value, 10);
    setFilterCensusYearId(yearId);
    // Reset to first page when filter changes
    setPagination((prev) => ({ ...prev, page: 1 }));
  };

  // Handle sort change
  const handleSortChange = (column: string) => {
    if (sortBy === column) {
      // Toggle sort order if clicking the same column
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      // Set new sort column and default to ascending
      setSortBy(column);
      setSortOrder('asc');
    }
    // Reset to first page when sort changes
    setPagination((prev) => ({ ...prev, page: 1 }));
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setPagination((prev) => ({ ...prev, page }));
  };

  // Handle page size change
  const handlePageSizeChange = (pageSize: number) => {
    setPagination((prev) => ({ ...prev, page: 1, pageSize }));
  };

  // Handle edit household
  const handleEditHousehold = (household: HouseholdWithDetails) => {
    setSelectedHousehold(household);
    setIsEditDialogOpen(true);
  };

  // Handle view household
  const handleViewHousehold = (household: HouseholdWithDetails) => {
    setSelectedHousehold(household);
    setIsViewDialogOpen(true);
  };

  // Handle household updated
  const handleHouseholdUpdated = () => {
    fetchHouseholds();
    setIsEditDialogOpen(false);
    setSelectedHousehold(null);

    // Force body to be interactive
    resetBodyStyles();

    showSuccess('householdUpdated');
  };

  // Handle edit dialogue open/close
  const handleEditDialogOpenChange = (open: boolean) => {
    setIsEditDialogOpen(open);
    if (!open) {
      // Reset selectedHousehold when dialogue is closed
      setSelectedHousehold(null);

      // Force body to be interactive
      resetBodyStyles();
    }
  };

  // Handle household deleted
  const handleHouseholdDeleted = async () => {
    // Immediately refresh the households list
    await fetchHouseholds();

    setIsViewDialogOpen(false);
    setSelectedHousehold(null);

    // Force body to be interactive
    resetBodyStyles();

    showSuccess('householdDeleted');
  };

  // Handle view dialogue open/close
  const handleViewDialogOpenChange = (open: boolean) => {
    setIsViewDialogOpen(open);
    if (!open) {
      // Reset selectedHousehold when dialogue is closed
      setSelectedHousehold(null);

      // Force body to be interactive
      resetBodyStyles();
    }
  };

  // Handle bulk delete completion (called after dialogue completes deletion)
  const handleDeleteSelected = async () => {
    // Refresh the households list
    await fetchHouseholds();

    // Success message is now handled by the centralized alert system
    // No need to show additional message here
  };

  // Clear all filters
  const clearAllFilters = () => {
    clearSearch();
    setFilterCensusYearId(null);
    setSortBy('id');
    setSortOrder('asc');
    setPagination((prev) => ({ ...prev, page: 1 }));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
        <div>
          <h1 className="font-bold text-2xl">{t('householdManagement')}</h1>
          <p className="text-muted-foreground">
            {t('householdManagementDescription')}
          </p>
        </div>
      </div>

      {/* Search and filter section */}
      <div className="rounded-lg border border-border/50 bg-background/50 backdrop-blur-sm">
        <div className="p-6 pb-3">
          <h3 className="font-semibold text-lg tracking-tight">
            {t('searchAndFilter')}
          </h3>
          <p className="text-muted-foreground text-sm">
            {t('findSpecificHouseholds')}
          </p>
        </div>
        <div className="px-6 pb-6">
          <div className="space-y-4">
            {/* Search bar with integrated filter button */}
            <div className="flex gap-2">
              <div className="relative flex-1">
                <Search
                  aria-hidden="true"
                  className="absolute top-3 left-0 h-4 w-4 text-muted-foreground"
                />
                <Input
                  aria-describedby="search-description"
                  aria-label={tCommon('searchHouseholds')}
                  autoComplete="off"
                  className="pr-20 pl-6"
                  id="household-search"
                  name="household-search"
                  onChange={handleSearchChange}
                  placeholder={t('pressFocusSearch')}
                  ref={searchInputRef}
                  type="search"
                  value={searchTerm}
                  variant="line"
                />
                <span className="sr-only" id="search-description">
                  {tAccessibility('searchHouseholdsDescription')}
                </span>
                {isSearching && (
                  <Loader2
                    aria-hidden="true"
                    className="absolute top-3 right-12 h-4 w-4 animate-spin text-muted-foreground"
                  />
                )}
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      aria-haspopup="dialog"
                      aria-label={tCommon('openFilterOptions')}
                      className="absolute top-1 right-1 h-7 cursor-pointer gap-1 px-2 text-muted-foreground hover:text-foreground"
                      id="filter-button"
                      size="sm"
                      variant="ghost"
                    >
                      <SlidersHorizontal
                        aria-hidden="true"
                        className="h-4 w-4"
                      />
                      <span className="sr-only sm:not-sr-only sm:whitespace-nowrap">
                        {tCommon('filters')}
                      </span>
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent
                    align="end"
                    aria-labelledby="filter-heading"
                    className="w-[240px] p-4"
                    role="dialog"
                    sideOffset={8}
                  >
                    <div className="grid gap-4">
                      <div className="space-y-2">
                        <h4
                          className="font-medium leading-none"
                          id="filter-heading"
                        >
                          {t('censusYear')}
                        </h4>
                        <Select
                          aria-label={tCommon('filterByCensusYear')}
                          name="census-year-filter"
                          onValueChange={handleCensusYearFilterChange}
                          value={
                            filterCensusYearId === null
                              ? 'all'
                              : filterCensusYearId.toString()
                          }
                        >
                          <SelectTrigger className="cursor-pointer">
                            <SelectValue placeholder={t('selectYear')} />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem className="cursor-pointer" value="all">
                              {tCommon('allYears')}
                            </SelectItem>
                            <SelectSeparator />
                            {censusYears
                              .sort((a, b) => b.year - a.year) // Sort by newest first
                              .map((year) => (
                                <SelectItem
                                  className="cursor-pointer"
                                  key={year.id}
                                  value={year.id.toString()}
                                >
                                  {year.year}{' '}
                                  {year.isActive
                                    ? t('activeStatus')
                                    : t('archivedStatus')}
                                </SelectItem>
                              ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <Button
                        className="mt-2"
                        onClick={clearAllFilters}
                        size="sm"
                        variant="outline"
                      >
                        {tCommon('resetFilters')}
                      </Button>
                    </div>
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            {/* Active filters display */}
            <div className="flex flex-wrap gap-2">
              {(searchTerm || filterCensusYearId !== null) && (
                <div className="flex flex-wrap items-center gap-2">
                  {searchTerm && (
                    <Badge
                      className="flex h-7 items-center gap-1 px-3"
                      variant="secondary"
                    >
                      <span>
                        {tCommon('search')}: {searchTerm}
                      </span>
                      <Button
                        className="-mr-1 ml-1 h-4 w-4 rounded-full"
                        onClick={clearSearch}
                        size="icon"
                        variant="ghost"
                      >
                        <X className="h-3 w-3" />
                        <span className="sr-only">
                          {t('removeSearchFilter')}
                        </span>
                      </Button>
                    </Badge>
                  )}
                  {filterCensusYearId !== null && (
                    <Badge
                      className="flex h-7 items-center gap-1 px-3"
                      variant="secondary"
                    >
                      <span>
                        {t('year')}:{' '}
                        {
                          censusYears.find((y) => y.id === filterCensusYearId)
                            ?.year
                        }
                        {censusYears.find((y) => y.id === filterCensusYearId)
                          ?.isActive
                          ? ` (${t('active')})`
                          : ` (${t('archived')})`}
                      </span>
                      <Button
                        className="-mr-1 ml-1 h-4 w-4 rounded-full"
                        onClick={() => setFilterCensusYearId(null)}
                        size="icon"
                        variant="ghost"
                      >
                        <X className="h-3 w-3" />
                        <span className="sr-only">Remove year filter</span>
                      </Button>
                    </Badge>
                  )}

                  {(searchTerm || filterCensusYearId !== null) && (
                    <Button
                      className="h-7 px-2 text-muted-foreground"
                      onClick={clearAllFilters}
                      size="sm"
                      variant="ghost"
                    >
                      {tCommon('clear')} {tCommon('selectAll').toLowerCase()}
                    </Button>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Households table */}
      <HouseholdTable
        activeCensusYear={activeCensusYear}
        households={households}
        loading={loading}
        onDeleteSelected={handleDeleteSelected}
        onEdit={handleEditHousehold}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
        onSortChange={handleSortChange}
        onView={handleViewHousehold}
        pagination={pagination}
        searchTerm={searchTerm}
        sortBy={sortBy}
        sortOrder={sortOrder}
      />

      {/* Edit household dialogue */}
      {selectedHousehold && (
        <EditHouseholdDialog
          censusYears={censusYears}
          household={selectedHousehold}
          onHouseholdUpdated={handleHouseholdUpdated}
          onOpenChange={handleEditDialogOpenChange}
          open={isEditDialogOpen}
        />
      )}

      {/* View household dialogue */}
      {selectedHousehold && (
        <ViewHouseholdDialog
          householdId={selectedHousehold.id}
          onHouseholdDeleted={handleHouseholdDeleted}
          onOpenChange={handleViewDialogOpenChange}
          open={isViewDialogOpen}
        />
      )}
    </div>
  );
}
