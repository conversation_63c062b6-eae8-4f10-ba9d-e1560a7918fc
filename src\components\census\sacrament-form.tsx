'use client';

import {
  AlertCircle,
  Calendar,
  CheckCircle2,
  MapPin,
  Trash2,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { memo } from 'react';
import { type Control, Controller } from 'react-hook-form';
//import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { DatePicker } from '@/components/ui/date-picker';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import type {
  ClientCombinedMemberSacramentFormValues,
  ClientSacramentFormValues,
} from '@/lib/validation/client/census-client';

interface SacramentType {
  id: number;
  code: string;
  name: string;
  description?: string;
}

interface SacramentFormProps {
  index: number;
  sacramentData: ClientSacramentFormValues;
  onFieldChange: (
    index: number,
    fieldName: keyof ClientSacramentFormValues,
    value: number | string | Date | null
  ) => void;
  onRemove: (index: number) => void;
  sacramentTypes: SacramentType[];
  allUsedSacramentTypeIds: number[];
  isTypeDisabledInOthers: (
    typeId: number,
    currentSacramentIndex: number
  ) => boolean;
  errors?: Partial<
    Record<keyof ClientSacramentFormValues, { message?: string }>
  >;
  control: Control<ClientCombinedMemberSacramentFormValues>;
}

export const SacramentForm = memo(function SacramentForm({
  index,
  sacramentData,
  onFieldChange,
  onRemove,
  sacramentTypes,
  // allUsedSacramentTypeIds is not used directly in this component
  // but is part of the interface for consistency
  isTypeDisabledInOthers,
  errors,
  control,
}: SacramentFormProps) {
  const t = useTranslations('census');
  const tForms = useTranslations('forms');
  const tSacraments = useTranslations('sacraments');

  const currentSacramentTypeDetails = sacramentTypes.find(
    (st) => st.id === sacramentData.sacramentTypeId
  );
  const _currentSacramentTypeName = currentSacramentTypeDetails
    ? tSacraments(currentSacramentTypeDetails.code as any)
    : tForms('selectType');
  const isTypeSelected = !!sacramentData.sacramentTypeId;

  let displayableSacramentTypes: SacramentType[];
  if (isTypeSelected) {
    if (currentSacramentTypeDetails) {
      displayableSacramentTypes = [currentSacramentTypeDetails];
    } else {
      displayableSacramentTypes = [];
    }
  } else {
    displayableSacramentTypes = sacramentTypes.filter(
      (st) => !isTypeDisabledInOthers(st.id, index)
    );
  }

  return (
    <div className="relative">
      {/* Remove button positioned in top-right corner */}
      <Button
        aria-label={t('removeSacrament')}
        className="absolute top-0 right-0 z-10 h-7 w-7 rounded-full p-0 text-muted-foreground transition-colors hover:bg-destructive/10 hover:text-destructive"
        onClick={() => onRemove(index)}
        size="sm"
        variant="ghost"
      >
        <Trash2 className="h-4 w-4" />
      </Button>

      {sacramentTypes.length > 0 &&
        !isTypeSelected &&
        sacramentTypes.every((type) =>
          isTypeDisabledInOthers(type.id, index)
        ) && (
          <div className="mt-2 mb-3 flex items-start gap-2 rounded-md border border-amber-200 bg-amber-50 p-2 dark:border-amber-800 dark:bg-amber-950">
            <AlertCircle className="mt-0.5 h-4 w-4 text-amber-600 dark:text-amber-400" />
            <p className="text-amber-800 text-sm dark:text-amber-300">
              {t('allSacramentTypesInUse')}
            </p>
          </div>
        )}

      <div className="mt-2 mb-4 flex flex-wrap gap-2">
        {displayableSacramentTypes.map((type) => {
          const isSelectedInThisForm =
            sacramentData.sacramentTypeId === type.id;

          const getSacramentGlassmorphism = (
            code: string,
            isSelected: boolean
          ) => {
            if (isSelected) {
              switch (code) {
                case 'baptism':
                  return 'bg-blue-600/25 border-blue-500/40 text-blue-800 dark:text-blue-200 shadow-lg backdrop-blur-md';
                case 'confirmation':
                  return 'bg-purple-600/25 border-purple-500/40 text-purple-800 dark:text-purple-200 shadow-lg backdrop-blur-md';
                case 'communion':
                  return 'bg-amber-600/25 border-amber-500/40 text-amber-800 dark:text-amber-200 shadow-lg backdrop-blur-md';
                case 'matrimony':
                  return 'bg-rose-600/25 border-rose-500/40 text-rose-800 dark:text-rose-200 shadow-lg backdrop-blur-md';
                default:
                  return 'bg-gray-600/25 border-gray-500/40 text-gray-800 dark:text-gray-200 shadow-lg backdrop-blur-md';
              }
            }
            switch (code) {
              case 'baptism':
                return 'bg-blue-50/60 border-blue-200/50 text-blue-700 hover:bg-blue-100/70 hover:border-blue-300/60 hover:text-blue-800 backdrop-blur-sm dark:bg-blue-900/25 dark:border-blue-700/50 dark:text-blue-300 dark:hover:bg-blue-800/35 dark:hover:border-blue-600/60 dark:hover:text-blue-200';
              case 'confirmation':
                return 'bg-purple-50/60 border-purple-200/50 text-purple-700 hover:bg-purple-100/70 hover:border-purple-300/60 hover:text-purple-800 backdrop-blur-sm dark:bg-purple-900/25 dark:border-purple-700/50 dark:text-purple-300 dark:hover:bg-purple-800/35 dark:hover:border-purple-600/60 dark:hover:text-purple-200';
              case 'communion':
                return 'bg-amber-50/60 border-amber-200/50 text-amber-700 hover:bg-amber-100/70 hover:border-amber-300/60 hover:text-amber-800 backdrop-blur-sm dark:bg-amber-900/25 dark:border-amber-700/50 dark:text-amber-300 dark:hover:bg-amber-800/35 dark:hover:border-amber-600/60 dark:hover:text-amber-200';
              case 'matrimony':
                return 'bg-rose-50/60 border-rose-200/50 text-rose-700 hover:bg-rose-100/70 hover:border-rose-300/60 hover:text-rose-800 backdrop-blur-sm dark:bg-rose-900/25 dark:border-rose-700/50 dark:text-rose-300 dark:hover:bg-rose-800/35 dark:hover:border-rose-600/60 dark:hover:text-rose-200';
              default:
                return 'bg-gray-50/60 border-gray-200/50 text-gray-700 hover:bg-gray-100/70 hover:border-gray-300/60 hover:text-gray-800 backdrop-blur-sm dark:bg-gray-800/25 dark:border-gray-600/50 dark:text-gray-300 dark:hover:bg-gray-700/35 dark:hover:border-gray-500/60 dark:hover:text-gray-200';
            }
          };

          return (
            <button
              className={cn(
                'group relative flex h-9 items-center justify-center rounded-md px-4 py-2 transition-all duration-300 hover:scale-105 active:scale-95',
                'cursor-pointer border font-medium text-sm',
                getSacramentGlassmorphism(type.code, isSelectedInThisForm)
              )}
              key={type.id}
              onClick={() => {
                onFieldChange(index, 'sacramentTypeId', type.id);
              }}
              type="button"
            >
              {/* Text */}
              <span className="relative z-10">
                {tSacraments(type.code as any)}
              </span>

              {/* Selected indicator */}
              {isSelectedInThisForm && (
                <CheckCircle2 className="relative z-20 ml-2 h-4 w-4" />
              )}

              {/* Glassmorphism overlay */}
              <div
                className={cn(
                  'absolute inset-0 z-10 rounded-md opacity-0 transition-opacity duration-300',
                  'bg-white/10 backdrop-blur-sm',
                  'group-hover:opacity-100'
                )}
              />
            </button>
          );
        })}
      </div>
      {errors?.sacramentTypeId && (
        <p className="mt-2 text-destructive text-xs">
          {errors.sacramentTypeId.message}
        </p>
      )}

      {isTypeSelected && (
        <div className="mt-4 space-y-4">
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div>
              <Label
                className="mb-2 flex items-center gap-1.5 font-medium text-sm"
                htmlFor={`date-${index}`}
              >
                <Calendar className="h-3.5 w-3.5 text-primary" />
                {t('date')}
              </Label>
              <Controller
                control={control}
                name={`sacraments.${index}.date` as const}
                render={({ field }) => (
                  <DatePicker
                    className={errors?.date ? 'border-destructive' : ''}
                    date={
                      field.value instanceof Date
                        ? field.value
                        : field.value
                          ? new Date(field.value)
                          : null
                    }
                    placeholderText={tForms('selectDatePlaceholder')}
                    preventFutureDates={true}
                    setDate={(newDate) => field.onChange(newDate)}
                  />
                )}
              />
              {errors?.date && (
                <p className="mt-1 text-destructive text-xs">
                  {errors.date.message}
                </p>
              )}
            </div>

            <div>
              <Label
                className="mb-2 flex items-center gap-1.5 font-medium text-sm"
                htmlFor={`place-${index}`}
              >
                <MapPin className="h-3.5 w-3.5 text-primary" />
                {t('place')}
              </Label>
              <Controller
                control={control}
                name={`sacraments.${index}.place` as const}
                render={({ field }) => (
                  <Input
                    id={`place-${index}`}
                    {...field}
                    className={cn(
                      'h-10', // Match the height of DatePicker
                      errors?.place ? 'border-destructive' : ''
                    )}
                    placeholder={tForms('enterPlaceOfSacramentPlaceholder')}
                  />
                )}
              />
              {errors?.place && (
                <p className="mt-1 text-destructive text-xs">
                  {errors.place.message}
                </p>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
});
