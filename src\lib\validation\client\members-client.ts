import { z } from 'zod/v4';
import { getTodayInSydney, startOfDay } from '@/lib/utils/date-time';

/**
 * Client-side validation schemas for member management forms
 * These schemas use translation functions for user-facing error messages
 * Use with useTranslations('validation') hook in client components
 */

/**
 * Create member search form validation schema with translations
 * @param t - Translation function from useTranslations('validation')
 */
export function createClientMemberSearchSchema(_t: any) {
  return z.object({
    page: z.number().min(1).default(1),
    pageSize: z.number().min(1).max(100).default(20),
    searchTerm: z.string().optional(),
    sortBy: z
      .enum([
        'id',
        'firstName',
        'lastName',
        'age',
        'gender',
        'mobilePhone',
        'suburb',
        'relationship',
        'census_year',
        'sacrament_count',
        'createdAt',
      ])
      .default('id'),
    sortOrder: z.enum(['asc', 'desc']).default('asc'),
    gender: z.enum(['male', 'female', 'other']).optional(),
    relationship: z
      .enum(['head', 'spouse', 'child', 'parent', 'relative', 'other'])
      .optional(),
    censusYearId: z.number().optional(),
    sacramentStatus: z.enum(['none', 'partial', 'complete']).optional(),
    minAge: z.number().min(0).max(150).optional(),
    maxAge: z.number().min(0).max(150).optional(),
  });
}

/**
 * Create member update form validation schema with translations
 * @param t - Translation function from useTranslations('validation')
 */
export function createClientUpdateMemberSchema(t: any) {
  return z.object({
    firstName: z
      .string()
      .min(1, { error: t('firstNameRequired') })
      .max(50, { error: t('firstNameTooLong') }),
    lastName: z
      .string()
      .min(1, { error: t('lastNameRequired') })
      .max(50, { error: t('lastNameTooLong') }),
    dateOfBirth: z.union([
      z.date().refine(
        (date) => {
          const today = getTodayInSydney();
          const dateToCheck = startOfDay(date);
          return dateToCheck <= today;
        },
        {
          error: t('futureDateNotAllowed'),
        }
      ),
      z.string().refine(
        (val) => {
          const parsedDate = new Date(val);
          if (Number.isNaN(parsedDate.getTime())) {
            return false; // Invalid date format
          }
          const today = getTodayInSydney();
          const dateToCheck = startOfDay(parsedDate);
          return dateToCheck <= today;
        },
        {
          error: t('futureDateNotAllowed'),
        }
      ),
    ]),
    gender: z.enum(['male', 'female', 'other'], {
      error: t('selectGender'),
    }),
    mobilePhone: z
      .string()
      .min(10, { error: t('phoneMinLength') })
      .max(10, { error: t('phoneMaxLength') })
      .regex(/^04\d{8}$/, { error: t('phoneNumbers') }),
    hobby: z
      .string()
      .max(100, { error: t('hobbyTooLong') })
      .optional()
      .or(z.literal('')),
    occupation: z
      .string()
      .max(100, { error: t('occupationTooLong') })
      .optional()
      .or(z.literal('')),
    relationship: z.enum(
      ['head', 'spouse', 'child', 'parent', 'relative', 'other'],
      {
        error: t('selectRelationship'),
      }
    ),
  });
}

/**
 * Create member creation form validation schema with translations
 * @param t - Translation function from useTranslations('validation')
 */
export function createClientCreateMemberSchema(t: any) {
  const updateSchema = createClientUpdateMemberSchema(t);

  return updateSchema.extend({
    householdId: z.number().min(1, { error: t('householdIdRequired') }),
    censusYearId: z.number().min(1, { error: t('censusYearIdRequired') }),
  });
}

/**
 * Create bulk member operations form validation schema with translations
 * @param t - Translation function from useTranslations('validation')
 */
export function createClientBulkMemberOperationSchema(t: any) {
  return z.object({
    memberIds: z
      .array(z.number())
      .min(1, { error: t('memberSelectionRequired') }),
    operation: z.enum(['delete', 'export', 'update_census_year']),
    newCensusYearId: z.number().optional(),
  });
}

// Type exports for client-side forms
export type ClientMemberSearchFormValues = z.infer<
  ReturnType<typeof createClientMemberSearchSchema>
>;
export type ClientUpdateMemberFormValues = z.infer<
  ReturnType<typeof createClientUpdateMemberSchema>
>;
export type ClientCreateMemberFormValues = z.infer<
  ReturnType<typeof createClientCreateMemberSchema>
>;
export type ClientBulkMemberOperationFormValues = z.infer<
  ReturnType<typeof createClientBulkMemberOperationSchema>
>;
