# WSCCC Census System Deployment Guide

## ⚠️ **CRITICAL DEPLOYMENT REQUIREMENT**

### 🔴 **ALWAYS USE ROOT ACCOUNT FOR ALL DEPLOYMENT OPERATIONS**

**MANDATORY**: After SSH connection, you **MUST** switch to root account before performing ANY deployment operations:

```bash
# 1. Connect as regular user
ssh -i "ssh/private-key" info@server-ip

# 2. IMMEDIATELY switch to root (REQUIRED)
sudo su -

# 3. Verify you're in root
whoami  # Must return 'root'

# 4. Now proceed with ALL deployment operations as root
```

**ALL of these operations REQUIRE root account:**
- ✅ `npm install` and `npm run build`
- ✅ `pm2` commands (start, stop, restart, save)
- ✅ File permission changes (`chown`, `chmod`)
- ✅ Directory creation and management
- ✅ Database operations
- ✅ Service management (nginx, postgresql)

**❌ FAILURE TO USE ROOT WILL CAUSE DEPLOYMENT FAILURES**

---

## Overview

This guide provides comprehensive instructions for deploying the WSCCC Census System to various environments, including VPS, cloud platforms, and local servers.

## Prerequisites

### System Requirements
- **Node.js**: v22.14.0 or higher
- **npm**: v11.4.2 or higher
- **PostgreSQL**: v15 or higher
- **Memory**: Minimum 2GB RAM (4GB recommended)
- **Storage**: Minimum 10GB available space
- **PM2**: Process manager for Node.js applications
- **SSH Access**: With private key authentication

### Required Software
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install -y nodejs npm postgresql postgresql-contrib nginx certbot python3-certbot-nginx pm2

# CentOS/RHEL
sudo yum install -y nodejs npm postgresql postgresql-server nginx certbot python3-certbot-nginx
npm install -g pm2
```

### SSH Key Setup
Ensure you have SSH key pair configured:
```bash
# Generate SSH key pair (if not exists)
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# Copy public key to server
ssh-copy-id -i ~/.ssh/id_rsa.pub user@your-server-ip

# Test SSH connection
ssh -i ~/.ssh/id_rsa user@your-server-ip "echo 'SSH connection successful'"
```

### Important Server Information
Before starting deployment, gather this information:
- **Server IP Address**: e.g., `*************`
- **SSH Username**: e.g., `info` (connect as non-root user for security)
- **SSH Private Key Path**: e.g., `ssh/gcp-census-key`
- **Database Name**: `wsccc_census_db_pg`
- **Database User**: `postgres`
- **Application Directory**: `/var/www/wsccc-census`
- **Domain Name**: e.g., `census.qzz.io` (for production)

### ⚠️ **CRITICAL: Always Use Root Account for Deployment Operations**

**IMPORTANT**: After SSH connection, you must **ALWAYS** switch to root account before performing any deployment operations:

```bash
# 1. Connect via SSH as regular user
ssh -i "ssh/private-key" info@server-ip

# 2. IMMEDIATELY switch to root (REQUIRED for all deployment operations)
sudo su -

# 3. Now you're in root account - proceed with deployment
# All npm, pm2, file operations, and builds MUST be done as root
```

**Why Root is Required:**
- File permissions management
- PM2 process management
- System-wide package installations
- Directory ownership changes
- Service management (nginx, postgresql)
- Build process requires elevated permissions

## Environment Configuration

### Environment Variables

Create `.env.local` file with the following variables:

```bash
# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/wsccc_census"
POSTGRES_URL_NON_POOLING="postgresql://username:password@localhost:5432/wsccc_census"

# Authentication Secrets (Generate unique values)
NEXTAUTH_SECRET_ADMIN="your-admin-secret-here"
NEXTAUTH_SECRET_CENSUS="your-census-secret-here"

# Application URLs
NEXTAUTH_URL="https://your-domain.com"
NEXT_PUBLIC_APP_URL="https://your-domain.com"

# AI Integration (Optional)
GOOGLE_GEMINI_API_KEY="your-gemini-api-key"

# Security Configuration
NODE_ENV="production"
```

### Generate Secrets
```bash
# Generate secure secrets
openssl rand -base64 32  # For NEXTAUTH_SECRET_ADMIN
openssl rand -base64 32  # For NEXTAUTH_SECRET_CENSUS
```

## Database Setup

### PostgreSQL Installation and Configuration

#### 1. Install PostgreSQL
```bash
# Ubuntu/Debian
sudo apt install postgresql postgresql-contrib

# Start and enable PostgreSQL
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

#### 2. Create Database and User
```bash
# Switch to postgres user
sudo -u postgres psql

# Create database and user
CREATE DATABASE wsccc_census;
CREATE USER wsccc_user WITH ENCRYPTED PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE wsccc_census TO wsccc_user;
\q
```

#### 3. Configure PostgreSQL
```bash
# Edit postgresql.conf
sudo nano /etc/postgresql/15/main/postgresql.conf

# Add/modify these settings:
listen_addresses = 'localhost'
max_connections = 100
shared_buffers = 256MB
effective_cache_size = 1GB
```

#### 4. Apply Database Schema
```bash
# Navigate to project directory
cd /path/to/wsccc-census-system

# Apply schema
psql -d wsccc_census -f database-postgresql.sql
```

## File Upload Guidelines

### ⚠️ **CRITICAL: Files to EXCLUDE from VPS Upload**

When uploading files to your VPS, **DO NOT** upload the following directories and files:

```bash
# ❌ NEVER UPLOAD THESE:
.git/                     # Git repository (use git clone instead)
.next/                    # Build output (will be generated on server)
.trae/                    # Development cache
.vercel/                  # Vercel deployment configuration
new_server/               # Local development server files
ssh/                      # SSH keys and certificates
test/                     # Test files and test data
temp_deploy/              # Temporary deployment files
node_modules/             # Dependencies (will be installed on server)
backups/                  # Local backup files
scripts/                  # Local utility scripts (not needed on server)
.env.local               # Local environment variables (server has its own)
.env.example             # Example environment file
.gitignore               # Git ignore file (not needed on server)
tsconfig.tsbuildinfo     # TypeScript build cache
README.md                # Documentation (not needed on server)
census-participant-flow.md # Documentation files
project-stats.mjs        # Local project analysis script

# Hidden files (if they exist):
.DS_Store                # macOS system files
Thumbs.db               # Windows system files
.vscode/                # VS Code editor settings
.idea/                  # IntelliJ IDEA settings
.eslintcache            # ESLint cache files
.stylelintcache         # Stylelint cache files

# ✅ UPLOAD THESE ESSENTIAL FILES:
app/                     # Application source code
components/              # React components
lang/                    # Language files
lib/                     # Utility libraries
public/                  # Static assets
src/                     # Source code
prisma/                  # Database schema
package.json             # Dependencies list
package-lock.json        # Dependency lock file
next.config.ts           # Next.js configuration
tsconfig.json            # TypeScript configuration
middleware.ts            # Next.js middleware
components.json          # Component configuration
postcss.config.mjs       # PostCSS configuration
biome.jsonc             # Biome linter configuration
eslint.config.mjs       # ESLint configuration
ecosystem.config.cjs     # PM2 configuration
```

### File Upload Methods

#### Method 1: SCP Upload (Recommended)
```bash
# Upload essential files only (from local project directory)
scp -i "path/to/private-key" -r \
  app components lang lib public src prisma \
  package.json package-lock.json next.config.ts tsconfig.json \
  middleware.ts components.json postcss.config.mjs biome.jsonc \
  user@server-ip:/var/www/project-name/

# Note: eslint.config.mjs and ecosystem.config.cjs should be uploaded if they exist
# Check if files exist before uploading:
if [ -f "eslint.config.mjs" ]; then
    scp -i "path/to/private-key" eslint.config.mjs user@server-ip:/var/www/project-name/
fi
if [ -f "ecosystem.config.cjs" ]; then
    scp -i "path/to/private-key" ecosystem.config.cjs user@server-ip:/var/www/project-name/
fi

# Alternative: Upload all files except excluded ones using rsync
rsync -avz \
  --exclude='.git' --exclude='.next' --exclude='.trae' --exclude='.vercel' \
  --exclude='new_server' --exclude='ssh' --exclude='test' --exclude='temp_deploy' \
  --exclude='node_modules' --exclude='backups' --exclude='scripts' \
  --exclude='.env.local' --exclude='.env.example' --exclude='.gitignore' \
  --exclude='tsconfig.tsbuildinfo' --exclude='README.md' \
  --exclude='census-participant-flow.md' --exclude='project-stats.mjs' \
  --exclude='.DS_Store' --exclude='Thumbs.db' --exclude='.vscode' \
  --exclude='.idea' --exclude='.eslintcache' --exclude='.stylelintcache' \
  -e "ssh -i path/to/private-key" \
  ./ user@server-ip:/var/www/project-name/
```

#### Method 2: Git Clone (Alternative)
```bash
# On the server
git clone https://github.com/your-username/wsccc-census-system.git
cd wsccc-census-system
```

## Application Deployment

### Method 1: Direct VPS Deployment

#### 1. Prepare Server Directory
```bash
# SSH into server and switch to root (REQUIRED)
ssh -i "ssh/private-key" info@server-ip
sudo su -

# Create and prepare directory (as root)
mkdir -p /var/www/wsccc-census
chown -R info:info /var/www/wsccc-census  # Set ownership to SSH user
```

#### 2. Upload Files (if not using git clone)
```bash
# From local machine - upload essential files only
scp -i "ssh/private-key" -r \
  app components lang lib public src prisma \
  package.json package-lock.json next.config.ts tsconfig.json \
  middleware.ts components.json postcss.config.mjs biome.jsonc \
  eslint.config.mjs ecosystem.config.cjs \
  user@server-ip:/var/www/wsccc-census/
```

#### 3. Install Dependencies
```bash
# ENSURE YOU ARE IN ROOT ACCOUNT (sudo su -)
cd /var/www/wsccc-census

# Install dependencies (as root)
npm install

# Install required ESLint packages for Next.js
npm install --save-dev eslint eslint-config-next
```

#### 4. Build Application
```bash
# Build the application (as root)
npm run build

# Fix file permissions after build
chown -R info:info /var/www/wsccc-census
chmod -R 755 /var/www/wsccc-census
```

#### 5. Database Setup and Restoration
```bash
# Upload database file (from local machine)
scp -i "ssh/private-key" "new_server/database-postgresql.sql" user@server-ip:/tmp/

# On the server - restore database (as root)
# ENSURE YOU ARE IN ROOT ACCOUNT (sudo su -)
sudo -u postgres psql -d wsccc_census_db_pg -f /tmp/database-postgresql.sql

# Clean up temporary file
rm -f /tmp/database-postgresql.sql
```

#### 6. Setup PM2 Process Manager
```bash
# ENSURE YOU ARE IN ROOT ACCOUNT (sudo su -)
# Install PM2 globally (if not already installed)
npm install -g pm2

# The ecosystem.config.cjs file should already be uploaded
# Start application using existing configuration (as root)
pm2 start ecosystem.config.cjs

# Save PM2 configuration for auto-restart on boot
pm2 save

# Setup PM2 to start on system boot (as root)
pm2 startup
# Follow the instructions provided by the startup command

# Verify PM2 is running
pm2 status
```

#### 7. Verify Deployment
```bash
# Check application status
pm2 status

# Test application response
curl -I http://localhost:3000

# Check application logs
pm2 logs wsccc-census

# Monitor real-time logs
pm2 logs wsccc-census --lines 50 -f
```

### Method 2: Docker Deployment

#### 1. Create Dockerfile
```dockerfile
FROM node:22-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm ci --only=production

# Copy application code
COPY . .

# Build application
RUN npm run build

# Expose port
EXPOSE 3000

# Start application
CMD ["npm", "start"]
```

#### 2. Create docker-compose.yml
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=****************************************/wsccc_census
    depends_on:
      - db
    restart: unless-stopped

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=wsccc_census
      - POSTGRES_USER=wsccc_user
      - POSTGRES_PASSWORD=secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database-postgresql.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped

volumes:
  postgres_data:
```

#### 3. Deploy with Docker
```bash
# Build and start services
docker-compose up -d

# View logs
docker-compose logs -f app
```

## Web Server Configuration

### Nginx Setup

#### 1. Install and Configure Nginx
```bash
# Install Nginx
sudo apt install nginx

# Create site configuration
sudo nano /etc/nginx/sites-available/wsccc-census
```

#### 2. Nginx Configuration
```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;

    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # Security Headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";

    # Proxy to Next.js application
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Static file caching
    location /_next/static/ {
        proxy_pass http://localhost:3000;
        add_header Cache-Control "public, max-age=31536000, immutable";
    }
}
```

#### 3. Enable Site and Restart Nginx
```bash
# Enable site
sudo ln -s /etc/nginx/sites-available/wsccc-census /etc/nginx/sites-enabled/

# Test configuration
sudo nginx -t

# Restart Nginx
sudo systemctl restart nginx
```

### SSL Certificate Setup

#### 1. Install Certbot
```bash
sudo apt install certbot python3-certbot-nginx
```

#### 2. Obtain SSL Certificate
```bash
# Get certificate for your domain
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# Test automatic renewal
sudo certbot renew --dry-run
```

## Firewall Configuration

### UFW (Ubuntu Firewall)
```bash
# Install UFW if not present
sudo apt install ufw

# Configure firewall rules
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw allow 5432  # PostgreSQL (if external access needed)

# Enable firewall
sudo ufw enable

# Check status
sudo ufw status
```

## Automated Deployment Script

### Create File Upload Deployment Script
```bash
#!/bin/bash
# deploy-upload.sh - File upload deployment script

set -e

# Configuration
LOCAL_PROJECT_DIR="."
REMOTE_USER="info"
REMOTE_HOST="your-server-ip"
REMOTE_DIR="/var/www/wsccc-census"
SSH_KEY="ssh/private-key"
BACKUP_DIR="/var/backups/wsccc-census"
DATE=$(date +%Y%m%d_%H%M%S)

echo "🚀 Starting WSCCC Census System deployment via file upload..."

# Validate SSH connection
echo "🔐 Testing SSH connection..."
ssh -i "$SSH_KEY" "$REMOTE_USER@$REMOTE_HOST" "echo 'SSH connection successful'"

# Create backup on remote server
echo "📦 Creating backup on remote server..."
ssh -i "$SSH_KEY" "$REMOTE_USER@$REMOTE_HOST" "
    sudo mkdir -p $BACKUP_DIR
    sudo tar -czf $BACKUP_DIR/backup_$DATE.tar.gz -C $REMOTE_DIR . 2>/dev/null || true
"

# Stop application
echo "⏹️ Stopping application..."
ssh -i "$SSH_KEY" "$REMOTE_USER@$REMOTE_HOST" "sudo su - -c 'cd $REMOTE_DIR && pm2 stop wsccc-census || true'"

# Upload essential files (excluding build artifacts and sensitive files)
echo "� Uploading application files..."
scp -i "$SSH_KEY" -r \
    app components lang lib public src prisma \
    package.json package-lock.json next.config.ts tsconfig.json \
    middleware.ts components.json postcss.config.mjs biome.jsonc \
    eslint.config.mjs ecosystem.config.cjs \
    "$REMOTE_USER@$REMOTE_HOST:$REMOTE_DIR/"

# Upload database file if it exists
if [ -f "new_server/database-postgresql.sql" ]; then
    echo "� Uploading database file..."
    scp -i "$SSH_KEY" "new_server/database-postgresql.sql" "$REMOTE_USER@$REMOTE_HOST:/tmp/"
fi

# Remote deployment steps
echo "🔧 Executing remote deployment steps..."
ssh -i "$SSH_KEY" "$REMOTE_USER@$REMOTE_HOST" "
    # CRITICAL: Switch to root account for ALL deployment operations
    sudo su - -c '
        echo \"🔴 OPERATING AS ROOT ACCOUNT (REQUIRED)\"
        whoami  # Should show root

        cd $REMOTE_DIR

        # Fix permissions
        chown -R info:info $REMOTE_DIR
        chmod -R 755 $REMOTE_DIR

        # Install/update dependencies (as root)
        npm install

        # Install ESLint packages if missing (as root)
        npm list eslint-config-next || npm install --save-dev eslint eslint-config-next

        # Build application (as root)
        npm run build

        # Update database if file exists (as root)
        if [ -f /tmp/database-postgresql.sql ]; then
            sudo -u postgres psql -d wsccc_census_db_pg -f /tmp/database-postgresql.sql
            rm -f /tmp/database-postgresql.sql
        fi

        # Fix permissions after build
        chown -R info:info $REMOTE_DIR
        chmod -R 755 $REMOTE_DIR

        # Start application (as root)
        pm2 restart wsccc-census || pm2 start ecosystem.config.cjs
        pm2 save

        # Verify deployment
        sleep 5
        if pm2 list | grep -q \"wsccc-census.*online\"; then
            echo \"✅ Deployment successful!\"
            curl -s -o /dev/null -w \"HTTP Status: %{http_code}\\nResponse Time: %{time_total}s\\n\" http://localhost:3000
        else
            echo \"❌ Deployment failed!\"
            exit 1
        fi
    '
"

echo "🎉 Deployment completed successfully!"
```

### Create Git-based Deployment Script
```bash
#!/bin/bash
# deploy-git.sh - Git-based deployment script

set -e

PROJECT_DIR="/var/www/wsccc-census"
BACKUP_DIR="/var/backups/wsccc-census"
DATE=$(date +%Y%m%d_%H%M%S)

echo "🚀 Starting WSCCC Census System deployment via Git..."

# Create backup
echo "📦 Creating backup..."
mkdir -p $BACKUP_DIR
tar -czf $BACKUP_DIR/backup_$DATE.tar.gz -C $PROJECT_DIR . 2>/dev/null || true

# Stop application
echo "⏹️ Stopping application..."
pm2 stop wsccc-census || true

# Update code
echo "� Updating code..."
cd $PROJECT_DIR
git pull origin main

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Install ESLint packages if missing
npm list eslint-config-next || npm install --save-dev eslint eslint-config-next

# Build application
echo "🔨 Building application..."
npm run build

# Set permissions
echo "🔐 Setting permissions..."
chown -R info:info $PROJECT_DIR
chmod -R 755 $PROJECT_DIR

# Start application
echo "▶️ Starting application..."
pm2 restart wsccc-census || pm2 start ecosystem.config.cjs
pm2 save

# Verify deployment
echo "✅ Verifying deployment..."
sleep 5
if pm2 list | grep -q "wsccc-census.*online"; then
    echo "✅ Deployment successful!"
    curl -s -o /dev/null -w "HTTP Status: %{http_code}\nResponse Time: %{time_total}s\n" http://localhost:3000
else
    echo "❌ Deployment failed!"
    exit 1
fi

echo "🎉 Deployment completed successfully!"
```

### Make Scripts Executable
```bash
# For file upload deployment
chmod +x deploy-upload.sh
./deploy-upload.sh

# For git-based deployment (run on server)
chmod +x deploy-git.sh
sudo ./deploy-git.sh
```

## Common Deployment Issues and Solutions

### ESLint Configuration Issues
If you encounter ESLint errors during build:
```bash
# Install required ESLint packages
npm install --save-dev eslint eslint-config-next

# Verify installation
npm list eslint eslint-config-next
```

### File Permission Issues
```bash
# Fix ownership and permissions
sudo chown -R info:info /var/www/wsccc-census
sudo chmod -R 755 /var/www/wsccc-census
```

### Database Connection Issues
```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Test database connection
psql -h localhost -U postgres -d wsccc_census_db_pg -c "SELECT 1;"
```

### PM2 Process Issues
```bash
# Check PM2 status
pm2 status

# Restart application
pm2 restart wsccc-census

# View logs
pm2 logs wsccc-census

# Reset PM2 if needed
pm2 delete all
pm2 start ecosystem.config.cjs
pm2 save
```

### Build Memory Issues
If build fails due to memory constraints:
```bash
# Increase Node.js memory limit
export NODE_OPTIONS="--max-old-space-size=4096"
npm run build

# Or build locally and upload .next folder
# (Not recommended for production)
```

## Monitoring and Maintenance

### Log Management
```bash
# PM2 logs
pm2 logs wsccc-census

# Nginx logs
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# System logs
journalctl -u nginx -f
```

### Database Backup
```bash
# Create backup script
cat > /usr/local/bin/backup-wsccc-db.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/var/backups/wsccc-census/db"
DATE=$(date +%Y%m%d_%H%M%S)
mkdir -p $BACKUP_DIR

pg_dump -h localhost -U wsccc_user wsccc_census | gzip > $BACKUP_DIR/wsccc_census_$DATE.sql.gz

# Keep only last 30 days of backups
find $BACKUP_DIR -name "*.sql.gz" -mtime +30 -delete
EOF

chmod +x /usr/local/bin/backup-wsccc-db.sh

# Add to crontab for daily backups
echo "0 2 * * * /usr/local/bin/backup-wsccc-db.sh" | sudo crontab -
```

### Health Checks
```bash
# Create health check script
cat > /usr/local/bin/health-check.sh << 'EOF'
#!/bin/bash
if curl -f http://localhost:3000/api/health > /dev/null 2>&1; then
    echo "✅ Application is healthy"
else
    echo "❌ Application is down, restarting..."
    pm2 restart wsccc-census
fi
EOF

chmod +x /usr/local/bin/health-check.sh

# Add to crontab for every 5 minutes
echo "*/5 * * * * /usr/local/bin/health-check.sh" | crontab -
```

## Security Considerations

### System Hardening
```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Configure automatic security updates
sudo apt install unattended-upgrades
sudo dpkg-reconfigure -plow unattended-upgrades

# Disable root login
sudo passwd -l root

# Configure SSH security
sudo nano /etc/ssh/sshd_config
# Set: PermitRootLogin no, PasswordAuthentication no
sudo systemctl restart ssh
```

### Application Security
- Use strong, unique secrets for authentication
- Enable HTTPS with proper SSL certificates
- Configure proper firewall rules
- Regular security updates
- Monitor application logs for suspicious activity
- Never upload sensitive files (.env.local, SSH keys, etc.) to the server
- Use proper file permissions (755 for directories, 644 for files)

## Quick Deployment Checklist

### Pre-deployment Checklist
- [ ] SSH key authentication configured
- [ ] Server has required software installed (Node.js, PostgreSQL, PM2, Nginx)
- [ ] Database created and configured
- [ ] Environment variables configured on server
- [ ] Firewall rules configured
- [ ] SSL certificate obtained (for production)

### File Upload Checklist
- [ ] **EXCLUDE**: `.git/`, `.next/`, `.trae/`, `.vercel/`, `new_server/`, `ssh/`, `test/`, `temp_deploy/`
- [ ] **EXCLUDE**: `node_modules/`, `backups/`, `scripts/`, `.env.local`, `.env.example`, `.gitignore`
- [ ] **EXCLUDE**: `tsconfig.tsbuildinfo`, `README.md`, `census-participant-flow.md`, `project-stats.mjs`
- [ ] **EXCLUDE**: `.DS_Store`, `Thumbs.db`, `.vscode/`, `.idea/`, `.eslintcache`, `.stylelintcache`
- [ ] **INCLUDE**: `app/`, `components/`, `lang/`, `lib/`, `public/`, `src/`, `prisma/`
- [ ] **INCLUDE**: `package.json`, `package-lock.json`, `next.config.ts`, `tsconfig.json`
- [ ] **INCLUDE**: `middleware.ts`, `components.json`, `postcss.config.mjs`, `biome.jsonc`
- [ ] **INCLUDE**: `eslint.config.mjs` (if exists), `ecosystem.config.cjs` (if exists)

### Post-deployment Checklist
- [ ] Dependencies installed successfully
- [ ] ESLint packages installed (`eslint`, `eslint-config-next`)
- [ ] Application built without errors
- [ ] Database restored/updated
- [ ] File permissions set correctly
- [ ] PM2 process running and saved
- [ ] Application responding to HTTP requests
- [ ] SSL certificate working (for production)
- [ ] Monitoring and backup scripts configured

### Performance Verification
```bash
# Check application response
curl -I http://localhost:3000

# Check PM2 status
pm2 status

# Monitor resource usage
htop

# Check disk space
df -h

# Check memory usage
free -h
```

## 🚀 New Engineer Quick Start Guide

### "I'm New Here" - 5-Minute Setup
If you're a new engineer taking over this project, follow these steps:

#### 1. **Understand the System** (2 minutes)
```bash
# Read these files first:
- README.md                    # Project overview
- guide/ARCHITECTURE.md        # System architecture
- guide/FEATURES.md           # Feature documentation
- This file (DEPLOYMENT.md)   # Deployment process
```

#### 2. **Gather Required Information** (1 minute)
Ask your team lead for:
- [ ] Server IP address
- [ ] SSH private key file
- [ ] SSH username
- [ ] Database credentials (should match local .env.local)
- [ ] Domain name (for production)

#### 3. **Test Local Setup** (1 minute)
```bash
# Ensure local development works
npm install
npm run build
npm run dev

# Test database connection
npm run db:push
```

#### 4. **Test Server Connection** (1 minute)
```bash
# Test SSH connection (replace with actual values)
ssh -i "ssh/your-private-key" username@server-ip "echo 'Connection successful'"

# CRITICAL: Test root access (required for deployment)
ssh -i "ssh/your-private-key" username@server-ip "sudo su - -c 'echo Root access confirmed'"

# If connection fails, check:
# - SSH key permissions: chmod 600 ssh/your-private-key
# - Server IP and username are correct
# - Firewall allows SSH (port 22)
# - User has sudo privileges for root access
```

### "I Need to Deploy Now" - 10-Minute Deployment
Use the automated script for quick deployment:

```bash
# 1. Update the deployment script with your server details
cp deploy-upload.sh my-deploy.sh
nano my-deploy.sh  # Update REMOTE_USER, REMOTE_HOST, SSH_KEY

# 2. Run deployment
chmod +x my-deploy.sh
./my-deploy.sh

# 3. Verify deployment
curl -I http://your-server-ip:3000
```

### "Something Broke" - Emergency Troubleshooting

#### Application Won't Start
```bash
# SSH into server
ssh -i "ssh/private-key" user@server-ip

# CRITICAL: Switch to root (ALWAYS REQUIRED)
sudo su -

# Check PM2 status (as root)
pm2 status

# Check logs (as root)
pm2 logs wsccc-census

# Common fixes (all as root):
pm2 restart wsccc-census
pm2 delete wsccc-census && pm2 start ecosystem.config.cjs

# If PM2 commands fail, ensure you're in root:
whoami  # Should return 'root'
```

#### Database Issues
```bash
# Test database connection
psql -h localhost -U postgres -d wsccc_census_db_pg -c "SELECT 1;"

# Check PostgreSQL status
sudo systemctl status postgresql

# Restart PostgreSQL if needed
sudo systemctl restart postgresql
```

#### Build Failures
```bash
# Check Node.js version
node --version  # Should be v22.14.0+

# Check npm version
npm --version   # Should be v11.4.2+

# Clear cache and rebuild
rm -rf node_modules .next
npm install
npm run build
```

### "I Need to Understand the Code" - Key Files
```bash
# Application entry points:
app/layout.tsx              # Main layout
app/[locale]/page.tsx       # Home page
middleware.ts               # Request middleware

# Database:
prisma/schema.prisma        # Database schema
lib/prisma.ts              # Database client

# Authentication:
app/api/auth/[...nextauth]/ # NextAuth configuration
lib/auth.ts                # Auth utilities

# Configuration:
next.config.ts             # Next.js configuration
ecosystem.config.cjs       # PM2 configuration
.env.local                 # Environment variables (local)
```

### "I Need Help" - Support Resources
1. **Check logs first**: `pm2 logs wsccc-census`
2. **Review this deployment guide**: All common issues are documented
3. **Check the troubleshooting guide**: `guide/TROUBLESHOOTING.md`
4. **Test locally first**: Ensure changes work in development
5. **Use the automated scripts**: They handle most edge cases

---

This comprehensive deployment guide ensures a secure, scalable, and maintainable production deployment of the WSCCC Census System with proper file management and security practices.
