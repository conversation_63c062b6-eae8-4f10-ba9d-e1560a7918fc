# WSCCC Census System Architecture Guide

## Introduction

The **WSCCC Census System** is a sophisticated, enterprise-grade web application designed specifically for Catholic community census management. Built with cutting-edge web technologies including Next.js 15.3.1, TypeScript 5, and PostgreSQL, it provides a secure, scalable platform for managing household and member information, tracking sacraments, and generating comprehensive analytics.

## Core Principles

- **Two Independent Authentication Systems**: Complete separation between admin and census participant authentication
- **UI Components**: All UI elements use `shadcn/ui` for consistent design and theming
- **Form System**: Centralised form components with Zod validation schemas
- **Validation System**: Zod v4 schemas in `lib/validation` for consistent data validation with enhanced performance
- **Centralized Alert System**: Unified toast/message handling with automatic translation and auth system separation
- **Date/Time Utility**: Centralised date/time handling with consistent formatting and timezone support
- **Security First**: Enterprise-grade security with CSP headers, rate limiting, and SQL injection protection
- **Performance Optimised**: Database connection pooling, caching strategies, and query optimisation

## Technology Stack

### Frontend
- **Next.js 15.3.1** - React framework with App Router
- **React 19** - UI library with latest features
- **TypeScript 5** - Type-safe development
- **shadcn/ui** - Modern UI component library
- **Tailwind CSS 4** - Utility-first CSS framework
- **Lucide React** - Icon library
- **Sonner** - Toast notifications
- **React Hook Form** - Form handling with validation

### Backend
- **Next.js API Routes** - Server-side API endpoints
- **NextAuth.js v4.24.11** - Authentication framework
- **Prisma ORM** - Type-safe database operations
- **PostgreSQL** - Primary database
- **Node.js** - Runtime environment

### Security & Authentication
- **Argon2id** - Password hashing (OWASP recommendation)
- **TOTP (otplib)** - Two-factor authentication
- **Rate Limiting** - Session-based protection
- **CSRF Protection** - HTTP-only cookies
- **Content Security Policy** - XSS protection

### AI & Analytics
- **Vercel AI SDK (ai v4.3.16)** - AI integration framework
- **Google Gemini 2.5 Flash** - AI language model for analytics chatbot
- **Recharts** - Data visualization library
- **Chart.js** - Additional charting capabilities

#### Chatbot Module Architecture

The AI-powered analytics chatbot has been refactored into a professional, modular architecture that promotes maintainability, scalability, and code quality.

**Architecture Principles:**
- **Domain-Driven Design**: Modules organized by functional domain
- **Security-First Architecture**: Dedicated security module with comprehensive protection
- **Performance Optimization**: Request caching with LRU cache implementation
- **Internationalization Support**: Multilingual system prompts (English/Chinese)

**Module Structure:**
```
src/lib/analytics/chatbot/
├── 🤖 ai/                     # AI Operations Module
│   ├── intent-analysis.ts     # Google Gemini intent detection
│   ├── system-prompts.ts      # Multilingual prompt engineering
│   ├── temperature.ts         # Dynamic temperature calculation
│   └── index.ts              # AI module exports
├── 🗄️ database/               # Database Operations Module
│   ├── intent-handlers.ts     # Intent-based query handlers
│   ├── temporal-intent-handlers.ts # Temporal analysis handlers
│   ├── distribution-queries.ts # Distribution table generators
│   ├── member-distributions.ts # Member-specific distributions
│   ├── household-distributions.ts # Household-specific distributions
│   ├── sacrament-distributions.ts # Sacrament-specific distributions
│   ├── keyword-queries.ts     # Keyword-based fallback queries
│   ├── keyword-query-executor.ts # Query execution with timeout
│   └── index.ts              # Database module exports
├── 🛡️ security/               # Security Module
│   ├── input-validation.ts    # Prompt injection detection
│   ├── error-handling.ts      # Secure error logging
│   ├── response-validation.ts # Response security filtering
│   ├── rate-limiting.ts       # LRU cache rate limiting
│   └── index.ts              # Security module exports
├── 🔧 utils/                  # Utilities Module
│   ├── cache.ts              # Request caching system
│   ├── analytics.ts          # Performance tracking
│   ├── helpers.ts            # Security and utility functions
│   └── index.ts              # Utils module exports
├── ⚙️ config.ts               # Centralized Configuration
└── 📋 validation.ts           # Zod schema validation
```

**Key Features:**
- **84.5% Code Reduction**: Main route file reduced from 3,495 to 542 lines
- **Chart Generation**: Support for waffle, pie, bar, line, area, scatter, heatmap, treemap, radar charts
- **Distribution Tables**: Automated statistical distribution generation
- **Intent Analysis**: AI-driven query interpretation with Google Gemini
- **Security Measures**: Prompt injection detection, rate limiting, secure error handling
- **Performance Optimization**: Request caching, PostgreSQL query optimization

### Internationalisation
- **next-intl 4.0** - Internationalisation framework
- **Zod v4** - Schema validation with translated error messages
- **Factory Functions** - Dynamic schema creation with locale-specific messages
- **Dual Language Support** - English and Chinese (zh-CN)
- **Type Safety** - Auto-generated TypeScript definitions

## System Architecture

### Dual Authentication Architecture

The system implements two completely independent authentication systems:

```mermaid
graph TB
    subgraph "Admin Portal (/admin/*)"
        A1[Admin Login] --> A2[NextAuth Admin Handler]
        A2 --> A3[admin-session-token]
        A3 --> A4[Admin Dashboard]
    end

    subgraph "Census Portal (/)"
        C1[Unique Code Entry] --> C2[NextAuth Census Handler]
        C2 --> C3[census-session-token]
        C3 --> C4[Census Form]
    end

    A2 -.->|NEXTAUTH_SECRET_ADMIN| A3
    C2 -.->|NEXTAUTH_SECRET_CENSUS| C3
```

#### Admin Authentication
- **Path**: `/admin/login`
- **Method**: Username/password with optional 2FA
- **Session**: JWT with 8-hour expiration
- **Cookies**: `admin-session-token`, `admin-callback-url`, `admin-csrf-token`
- **Secret**: `NEXTAUTH_SECRET_ADMIN`
- **Middleware Protection**: `authenticateAdminApi()` and `authenticateAdminPage()`
- **Session Isolation**: SessionIsolationProvider with `admin-session-token` validation

#### Census Authentication
- **Path**: `/` (homepage with unique code entry)
- **Method**: Variable Validation Range Security + Hybrid Rate Limiting
- **Code Format**: `cc-yyyy-xxxxxxxxxxxxxxx` (23 characters)
- **Validation**: Only 8-14 characters validated (40-60% are decoys)
- **Security**: SHA-256 hashing of variable character subset
- **Session**: JWT with 8-hour expiration
- **Cookies**: `census-session-token`, `census-callback-url`, `census-csrf-token`
- **Secret**: `NEXTAUTH_SECRET_CENSUS`
- **Middleware Protection**: `authenticateCensusApi()` and `authenticateCensusPage()`
- **Session Isolation**: SessionIsolationProvider with `census-session-token` validation
- **Rate Limiting**: Hybrid client-server progressive timeout system
  - Client: Smooth countdown UX without polling
  - Server: Authoritative validation on every auth attempt
  - Escalation: 1min → 5min → 15min → 30min → 60min

### Middleware Architecture

#### Authentication Middleware Flow
The system uses a sophisticated middleware layer for request processing and authentication:

1. **Static Asset Handling**: Immediate passthrough for static files
2. **Auth System Isolation Guard**: Prevents cross-auth contamination
3. **API Route Authentication**: Dual authentication system for API endpoints
4. **i18n Routing**: Internationalization support
5. **Page Authentication**: Protected page access control

#### Secure Authentication Flow

**Census Authentication with Variable Validation Range Security:**

```
1. User enters unique code: cc-2025-abcdefghijklmno
2. Rate limiting check: Client asks server for current status
3. Server validation: If not locked, proceed with authentication
4. Code lookup: Find code in database with validation range
5. Character extraction: Extract 8-14 chars from positions 8-22
6. Hash comparison: SHA-256(extracted) vs stored validation hash
7. Success: Create JWT session with 8-hour expiration
8. Failure: Record attempt, calculate lockout if needed
```

**Hybrid Rate Limiting Flow:**

```
1. Strategic server check: Before every auth attempt
2. Database authority: Server checks lockout status
3. Client countdown: Smooth UX without server polling
4. Multi-tab sync: localStorage events keep tabs synchronized
5. Graceful degradation: Works even when client features fail
6. Server validation: Every auth attempt validated regardless of client state
```

#### Endpoint Constants and Validation
```typescript
// Public census endpoints (always accessible)
const PUBLIC_CENSUS_ENDPOINTS = [
  '/api/census/auth/rate-limit-status',
  '/api/census/status',
];

// NextAuth.js session endpoints (cross-auth sensitive)
const NEXTAUTH_SESSION_ENDPOINTS = [
  '/api/census/auth/session',
  '/api/census/auth/signin',
  '/api/census/auth/signout',
  // ... additional endpoints
];

// Admin authentication endpoints
const ADMIN_AUTH_ENDPOINTS = [
  '/api/auth/session',
  '/api/auth/signin',
  '/api/auth/signout',
  // ... additional endpoints
];
```

#### SessionIsolationProvider Integration
- **Purpose**: Prevents NextAuth.js session requests when wrong cookies present
- **Implementation**: Wraps SessionProvider with cookie validation
- **Behavior**: Provides null session context when expected cookies missing
- **Performance**: Zero network requests for mismatched auth contexts

### Data Flow Architecture

```mermaid
graph TB
    subgraph Client["🖥️ Client Layer"]
        CP["Census Portal<br/>Public Entry Point"]
        AP["Admin Portal<br/>Administrative Interface"]
        UI["shadcn/ui Components<br/>Modern UI Library"]
        AUTH["Dual Auth Providers<br/>NextAuth.js Sessions"]
    end

    subgraph Server["⚙️ Server Layer"]
        MW["Middleware<br/>Route Protection"]
        CAPI["Census API Routes<br/>/api/census/*"]
        AAPI["Admin API Routes<br/>/api/admin/*"]
        AI["AI Analytics API<br/>Vercel AI SDK"]
        VALID["Validation Layer<br/>Zod Schemas"]
    end

    subgraph Database["🗄️ Database Layer"]
        PG["PostgreSQL Database<br/>Prisma ORM"]
        CORE["Core Models<br/>Members, Households"]
        AUTHM["Auth Models<br/>Admin, Sessions"]
        SYS["System Models<br/>Settings, Controls"]
    end

    CP --> MW
    AP --> MW
    MW --> CAPI
    MW --> AAPI
    CAPI --> VALID
    AAPI --> VALID
    VALID --> PG
    AI --> PG
```

### Server-Side Rendering (SSR) Architecture

The system uses strategic Server-Side Rendering with `force-dynamic` for security-critical components that require fresh session data.

#### SSR Session Management Pattern

```typescript
// Layout components requiring fresh session data
export const dynamic = 'force-dynamic';

export default async function SecurityLayout() {
  // Always gets fresh session data from server
  const session = await getServerSession(authOptions);

  // Security-critical authorization decisions
  const isAuthorized = session?.user?.role === 'admin';
  const hasPermissions = session?.user?.permissions?.includes('manage');

  return <ProtectedComponent isAuthorized={isAuthorized} />;
}
```

#### Implementation Strategy

**Census Layout (`/census/layout.tsx`)**:
- **Purpose**: Controls progress tracker visibility based on registration status
- **Security**: Server-side validation of `householdId` for UI authorization
- **Performance**: Dynamic rendering ensures immediate UI updates after registration

**Admin Layout (`/admin/layout.tsx`)**:
- **Purpose**: Displays admin user information and session validation
- **Security**: Server-side admin role validation and permissions
- **Consistency**: Fresh session data for accurate user state display

#### Security Benefits

```mermaid
graph TB
    subgraph "SSR Security Flow"
        REQ[Client Request]
        SSR[Server Component]
        AUTH[getServerSession]
        DB[Database Validation]
        UI[Authorized UI]
    end

    subgraph "Session Validation"
        JWT[JWT Token]
        FRESH[Fresh Session Data]
        CACHE[No Stale Cache]
    end

    REQ --> SSR
    SSR --> AUTH
    AUTH --> JWT
    JWT --> FRESH
    FRESH --> DB
    DB --> UI

    CACHE -.->|Prevented by force-dynamic| AUTH
```

#### Performance Considerations

- **Selective Application**: Only applied to layouts requiring fresh session data
- **Minimal Impact**: Affects security-critical components, not entire application
- **User Experience**: Immediate UI updates without page refresh requirements
- **Caching Strategy**: Static content remains cached, only session-dependent content is dynamic

### Session Isolation Architecture

The system maintains strict separation between admin and census authentication systems to prevent session contamination and ensure security.

#### CombinedAuthProvider Pattern

```typescript
// Prevents CLIENT_FETCH_ERROR with pathname-based provider selection
const renderAuthProvider = () => {
  const isAdminPath = pathname?.startsWith('/admin');

  // Admin provider for admin pages only
  if (isAdminPath) {
    return <SessionProvider basePath="/api/auth">{children}</SessionProvider>;
  }

  // Census provider for homepage, census pages, and public pages
  return (
    <SessionProvider
      basePath="/api/census/auth"
      refetchOnWindowFocus={false}
      refetchInterval={0}
    >
      {children}
    </SessionProvider>
  );
};
```

#### Cross-Context Navigation Pattern

```typescript
// Full page navigation for cross-authentication contexts
// Prevents CLIENT_FETCH_ERROR during navigation

// ✅ Admin to Homepage (Full page navigation)
<button onClick={() => window.location.href = '/'}>
  Homepage
</button>

// ✅ Census to Admin Login (Full page navigation)
<button onClick={() => window.location.href = '/admin/login'}>
  Admin Login
</button>

// ❌ Avoid client-side navigation for cross-context
<Link href="/admin/login">Admin Login</Link> // Can cause CLIENT_FETCH_ERROR
```

#### Session Contamination Prevention

```mermaid
graph TB
    subgraph "Admin Session Flow"
        ADMIN[Admin Portal]
        ADMIN_AUTH[Admin Auth Provider]
        ADMIN_API[/api/auth/*]
    end

    subgraph "Census Session Flow"
        HOME[Homepage]
        CENSUS[Census Portal]
        CENSUS_AUTH[Census Auth Provider]
        CENSUS_API[/api/census/auth/*]
    end

    subgraph "Navigation Protection"
        NAV[Navigation Event]
        PATH_CHECK{Current Path Check}
        PROVIDER_SWITCH[Provider Selection]
    end

    ADMIN --> ADMIN_AUTH
    ADMIN_AUTH --> ADMIN_API

    HOME --> CENSUS_AUTH
    CENSUS --> CENSUS_AUTH
    CENSUS_AUTH --> CENSUS_API

    NAV --> PATH_CHECK
    PATH_CHECK -->|/admin/*| ADMIN_AUTH
    PATH_CHECK -->|Other paths| CENSUS_AUTH
    PATH_CHECK --> PROVIDER_SWITCH
```

#### Security Benefits

- **Zero Cross-Contamination**: Admin and census sessions never interfere
- **Pathname-Based Routing**: Dynamic provider selection based on current location
- **Full Page Navigation**: Cross-context links use complete page refresh for clean transitions
- **Error Prevention**: Eliminates CLIENT_FETCH_ERROR from navigation conflicts
- **Session Integrity**: Each system maintains independent session state
- **Industry Standard**: Follows best practices used by major platforms (Shopify, WordPress, etc.)
- **Defense-in-Depth**: Multiple protection layers ensure robust security

### Current Authentication Solution (2025)

The WSCCC Census System implements a **production-ready dual authentication architecture** that completely eliminates CLIENT_FETCH_ERROR issues through a comprehensive defense-in-depth approach.

#### Problem Solved

**Previous Issue**: CLIENT_FETCH_ERROR when navigating between admin and census portals due to session cookie conflicts during client-side navigation.

**Root Cause**: Client-side navigation (`<Link>`) between different authentication contexts caused NextAuth to receive wrong session cookies, resulting in JSON parsing errors.

#### Current Solution Architecture

**Layer 1: Pathname-Based Session Provider Selection**
```typescript
// Automatic provider selection based on current route
const isAdminPath = pathname?.startsWith('/admin');
const provider = isAdminPath ? 'admin' : 'census';
```

**Layer 2: Full Page Navigation for Cross-Context Links**
```typescript
// Industry standard approach for cross-authentication navigation
onClick={() => window.location.href = '/target-url'}
```

**Layer 3: Middleware Protection (Fallback)**
```typescript
// Additional security layer for edge cases
if (isNextAuthEndpoint && wrongCookieDetected) {
  return NextResponse.json({ error: 'AUTH_SYSTEM_MISMATCH' });
}
```

#### Implementation Details

**Fixed Components**:
- `src/components/admin/user-dropdown.tsx` - Homepage navigation
- `src/components/home/<USER>
- `src/components/home/<USER>
- `src/components/home/<USER>
- `app/[locale]/admin/login/login-page.tsx` - Homepage links

**Navigation Patterns**:
```typescript
// ✅ Same-context navigation (fast client-side)
<Link href="/admin/dashboard">Dashboard</Link>
<Link href="/census/form">Census Form</Link>

// ✅ Cross-context navigation (full page refresh)
<button onClick={() => window.location.href = '/admin/login'}>
  Admin Login
</button>
<button onClick={() => window.location.href = '/'}>
  Homepage
</button>
```

#### Performance Characteristics

- **Same-Context Navigation**: Instant client-side routing (unchanged)
- **Cross-Context Navigation**: ~1-2 second full page load (reliable)
- **Error Rate**: 0% CLIENT_FETCH_ERROR (eliminated)
- **User Experience**: Seamless with clear context transitions

#### Security Guarantees

- **Complete Session Isolation**: Zero cross-contamination between auth systems
- **Reliable Navigation**: 100% success rate for cross-context transitions
- **Industry Compliance**: Follows authentication best practices
- **Future-Proof**: Scalable architecture for additional auth contexts

### Centralized Alert System Architecture

The system implements a **production-ready centralized alert system** that provides unified message handling with automatic translation and complete auth system separation.

```mermaid
graph TB
    subgraph "Client Layer"
        UC[useMessage Hook]
        AC[AlertContext]
        TH[Toast Handlers]
        UT[useToastTranslation]
    end

    subgraph "Server Layer"
        SM[setServerMessage]
        AR[API Responses]
        SC[Server Cookies]
    end

    subgraph "Message Mappings"
        SM1[successMessageKeys]
        EM1[authErrorKeys]
        EM2[censusErrorKeys]
        EM3[settingsErrorKeys]
        WM[warningMessageKeys]
        IM[infoMessageKeys]
    end

    subgraph "Translation System"
        EN[English Translations]
        ZH[Chinese Translations]
        NS[Translation Namespaces]
    end

    subgraph "Auth Separation"
        AT[auth_toast Cookie]
        CT[census_toast Cookie]
        ADM[Admin Context]
        CEN[Census Context]
    end

    UC --> AC
    AC --> TH
    UC --> UT
    SM --> SC
    AR --> SM

    UC --> SM1
    UC --> EM1
    UC --> EM2
    UC --> EM3
    UC --> WM
    UC --> IM

    SM1 --> NS
    EM1 --> NS
    EM2 --> NS
    EM3 --> NS
    WM --> NS
    IM --> NS

    NS --> EN
    NS --> ZH

    ADM --> AT
    CEN --> CT
    AT --> TH
    CT --> TH
```

#### Key Features

- **🎯 Unified Interface**: Single `useMessage()` hook for all message types
- **🔄 Automatic Translation**: Context-aware translation with locale detection
- **🔐 Auth System Separation**: Independent admin and census message systems
- **📱 SSR/CSR Support**: Both server-side and client-side message handling
- **✅ 100% Coverage**: All toast messages fully translated and verified
- **🚀 Performance Optimized**: Deduplication, caching, and efficient rendering

#### Message Flow

1. **Client-Side**: Components use `useMessage()` hook
2. **Context Detection**: Automatic admin vs census context detection
3. **Translation**: Message keys mapped to translated strings
4. **Display**: Sonner toast system with deduplication
5. **Server-Side**: API routes use `setServerMessage()` for server-to-client messages
6. **Cookie Transport**: HTTP-only cookies transport messages between server and client

## Database Schema

### Core Models

#### Members
- Personal information (name, DOB, gender, phone)
- Relationship to household
- Sacrament records
- Hobby information

#### Households
- Location data (suburb)
- Census year tracking
- Member relationships
- Unique code assignments

#### CensusYears
- Year tracking
- Active/inactive status
- Historical data management

#### UniqueCode
- **Revolutionary Variable Validation Range Security**:
  - Each code validates only 8-14 characters from positions 8-22 (0-based)
  - Variable validation patterns make systematic enumeration infeasible
  - 40-60% decoy characters provide no authentication value
- **Cryptographic Security**:
  - SHA-256 hashing of extracted validation characters
  - Only hash stored in database, not validation pattern
  - Cryptographic comparison prevents tampering
- **Code Format**: `cc-yyyy-xxxxxxxxxxxxxxx` (23 characters)
  - Positions 0-7: Fixed prefix `cc-yyyy-`
  - Positions 8-22: Random data with variable validation subset
- **Attack Resistance**: Systematic enumeration computationally infeasible
- **Assignment Tracking**: Household assignment and usage monitoring
- **QR Code Integration**: Secure print cards with validation data

### Authentication Models

#### Admin
- Username/password authentication
- Role-based access control
- Two-factor authentication support
- Audit logging

#### UserSessions
- Custom session management
- Enhanced security tracking
- Session invalidation

#### AuthRateLimit
- Rate limiting implementation
- Progressive lockout system
- Session-based tracking

### System Models

#### SystemSettings
- Configurable system parameters
- Church information
- Census controls
- Feature toggles

## Project Structure

```
wsccc-census-system/
├── 📁 app/                          # Next.js 15 App Router
│   ├── 🔐 admin/                   # Admin Portal Pages
│   │   ├── dashboard/              # Admin dashboard with analytics
│   │   ├── household/              # Household management
│   │   ├── members/                # Member management
│   │   ├── unique-code/            # Code generation & QR printing
│   │   ├── analytics/              # AI chatbot & reporting
│   │   ├── settings/               # System configuration
│   │   └── login/                  # Admin authentication
│   ├── 🌐 api/                     # API Routes
│   │   ├── admin/                  # Admin-only endpoints
│   │   ├── census/                 # Census participant endpoints
│   │   ├── auth/                   # Admin authentication
│   │   └── database/               # Database utilities
│   ├── 🏠 census/                  # Census Portal Pages
│   │   └── [code]/                 # Dynamic code-based routing
│   └── globals.css                 # Global styles
├── 📁 src/                         # Source Code
│   ├── 🎨 components/              # React Components
│   │   ├── ui/                     # shadcn/ui components
│   │   ├── admin/                  # Admin-specific components
│   │   ├── census/                 # Census-specific components
│   │   ├── forms/                  # Form components
│   │   └── layout/                 # Layout components
│   ├── 📚 lib/                     # Utility Libraries
│   │   ├── auth/                   # Authentication utilities
│   │   ├── db/                     # Database operations
│   │   ├── utils/                  # General utilities
│   │   ├── validation/             # Zod schemas
│   │   └── cache/                  # Caching system
│   ├── 🎣 hooks/                   # Custom React hooks
│   └── 📝 types/                   # TypeScript definitions
├── 📁 prisma/                      # Database Schema
│   └── schema.prisma               # Prisma schema definition
├── 📁 guide/                       # Documentation
├── 📁 scripts/                     # Utility scripts
└── 📁 new_server/                  # Deployment scripts
```

## Key Features

### Admin Portal
- **Dashboard**: Analytics and system overview
- **Household Management**: CRUD operations for households
- **Member Management**: Individual member records
- **Unique Code Management**: Code generation and QR printing
- **AI Analytics**: Natural language database queries
- **System Settings**: Configuration management
- **Data Export**: CSV, JSON, SQL formats

### Census Portal
- **Unique Code Login**: Secure participant access
- **Household Registration**: First-time setup
- **Census Form**: Member and sacrament data collection
- **Account Management**: Self-service options

### Security Architecture

#### Revolutionary Security Systems

**1. Variable Validation Range Security**
```
Code: cc-2025-abcdefghijklmno
      0123456789012345678901234  ← Position index (0-based)

Fixed:     Positions 0-7   = "cc-2025-" (8 characters)
Random:    Positions 8-22  = "abcdefghijklmno" (15 characters)
Validated: Variable subset  = 8-14 characters from positions 8-22
Decoy:     40-60% ignored  = Meaningless for authentication
```

**2. Hybrid Rate Limiting Architecture**
```
┌─────────────────┐    Strategic     ┌──────────────────┐
│   Client Timer  │ ────Validation──▶│  Server (Auth)   │
│  (Smooth UX)    │                  │  (Authoritative) │
└─────────────────┘                  └──────────────────┘
         │                                     │
         ▼                                     ▼
   ┌─────────────┐                    ┌──────────────┐
   │ localStorage │                    │   Database   │
   │ (Multi-tab)  │                    │ (Persistent) │
   └─────────────┘                    └──────────────┘
```

#### Core Security Features
- **Variable Validation Range Security**: Revolutionary unique code authentication
- **Hybrid Rate Limiting**: Client UX + server security (95% network reduction)
- **Two-Factor Authentication**: TOTP support for admins
- **Audit Logging**: Complete action tracking
- **Input Validation**: Comprehensive Zod schemas
- **CSRF Protection**: HTTP-only secure cookies
- **Content Security Policy**: XSS prevention
- **Session Isolation**: Zero cross-contamination between admin/census systems

### Performance Features
- **Database Connection Pooling**: Optimised connections
- **Caching System**: In-memory cache with TTL
- **Query Optimization**: Efficient database operations
- **Lazy Loading**: Component-level optimization

## Deployment Architecture

### Environment Configuration
- **Development**: Local PostgreSQL with debug logging
- **Production**: Managed PostgreSQL with optimised settings
- **Environment Variables**: Secure configuration management

### Security Headers
- **X-Frame-Options**: Clickjacking protection
- **X-Content-Type-Options**: MIME sniffing prevention
- **Content-Security-Policy**: Resource loading restrictions
- **Permissions-Policy**: Browser feature controls

This architecture provides a robust, scalable foundation for Catholic community census management with enterprise-grade security and modern user experience.

---

# Complete Visual System Architecture

## Complete Dual Authentication System Architecture

The following comprehensive visual representation illustrates the WSCCC Census System's dual authentication architecture, including the hybrid defense-in-depth security implementation.

### System Overview

The WSCCC Census System implements a sophisticated dual authentication architecture with complete isolation between admin and census participant systems. The architecture employs a hybrid defense-in-depth approach to prevent cross-contamination while maintaining optimal performance and user experience.

### Key Architectural Principles

1. **Complete System Isolation**: Zero contamination between admin and census authentication systems
2. **Defense-in-Depth Security**: Multi-layer protection against authentication conflicts
3. **Performance Optimization**: Minimal overhead with intelligent session management
4. **Enterprise Security**: Professional-grade authentication and session handling
5. **Scalable Design**: Modular architecture supporting future enhancements

## Authentication System Architecture

This diagram shows how users authenticate and access the system through our intelligent dual authentication architecture:

```mermaid
flowchart TD
    %% =============================================================================
    %% USER AUTHENTICATION JOURNEYS - How Different Users Sign In
    %% =============================================================================
    subgraph "🔐 User Authentication Journeys"
        direction TB

        subgraph "👨‍💼 Admin Authentication Journey"
            AdminLogin[👨‍💼 Admin Login<br/>🔑 Username + Password<br/>📱 2FA Required<br/>🛡️ High Security]
        end

        subgraph "👥 Census Authentication Journey"
            CensusLogin[👥 Census Login<br/>🎫 Unique Family Code<br/>🏠 Household Access<br/>✅ Simple & Secure]
        end

        subgraph "🌍 Public Access Journey"
            PublicAccess[🌍 Public Access<br/>ℹ️ No Authentication<br/>📊 Status Information<br/>🔍 Rate Limited]
        end
    end

    %% =============================================================================
    %% INTELLIGENT AUTHENTICATION ROUTER - Smart Context Detection
    %% =============================================================================
    subgraph "🎯 Intelligent Authentication Router"
        direction TB

        AuthRouter[🎯 Smart Authentication Router<br/>🔍 Context Detection<br/>🚦 Traffic Routing<br/>🛡️ Security Enforcement]

        subgraph "🔒 Secure Authentication Channels"
            direction LR
            AdminChannel[🔒 Admin Channel<br/>NextAuth + 2FA<br/>NEXTAUTH_SECRET_ADMIN<br/>8-hour Sessions]
            CensusChannel[🔑 Census Channel<br/>Code Validation<br/>NEXTAUTH_SECRET_CENSUS<br/>Account Protection]
            PublicChannel[🌍 Public Channel<br/>No Authentication<br/>Rate Limiting<br/>Read-only Access]
        end
    end

    %% =============================================================================
    %% SECURITY PROTECTION SYSTEM - Defense in Depth
    %% =============================================================================
    subgraph "🛡️ Security Protection System"
        direction TB

        SecurityGuard[🛡️ Security Guard<br/>🚫 Cross-System Prevention<br/>⏱️ Rate Limiting<br/>✅ Request Validation]

        subgraph "🎯 Smart Endpoint Routing"
            direction LR
            AdminEndpoints[👨‍💼 Admin Endpoints<br/>/api/admin/*<br/>🔐 Secure Management<br/>🤖 AI Analytics]
            CensusEndpoints[👥 Census Endpoints<br/>/api/census/*<br/>🏠 Family Registration<br/>📝 Form Management]
            PublicEndpoints[🌍 Public Endpoints<br/>📊 Status Check<br/>🔍 Rate Limit Info<br/>ℹ️ System Health]
        end
    end

    %% =============================================================================
    %% SECURE SESSION MANAGEMENT - Token & Cookie Handling
    %% =============================================================================
    subgraph "🍪 Secure Session Management"
        direction LR

        subgraph "🔒 Admin Session Security"
            AdminSessions[🔒 Admin Sessions<br/>admin-session-token<br/>admin-csrf-token<br/>HttpOnly + Secure]
        end

        subgraph "🔑 Census Session Security"
            CensusSessions[🔑 Census Sessions<br/>census-session-token<br/>census-csrf-token<br/>Account Deletion Protection]
        end

        subgraph "📊 Session Storage"
            SessionStore[📊 Session Store<br/>JWT Token Management<br/>8-hour Expiration<br/>Automatic Renewal]
        end
    end

    %% =============================================================================
    %% DATA SECURITY FOUNDATION - Protected Information Storage
    %% =============================================================================
    subgraph "🗄️ Data Security Foundation"
        direction LR

        subgraph "👨‍💼 Admin Data Vault"
            AdminDB[(🔐 Admin Database<br/>User Credentials<br/>2FA Settings<br/>Security Audit Log)]
        end

        subgraph "👥 Census Data Vault"
            CensusDB[(🏠 Census Database<br/>Family Information<br/>Member Records<br/>Sacrament Data)]
        end
    end

    %% =============================================================================
    %% BEAUTIFUL AUTHENTICATION FLOWS - User Journey Connections
    %% =============================================================================

    %% User Authentication Journeys
    AdminLogin --> AuthRouter
    CensusLogin --> AuthRouter
    PublicAccess --> AuthRouter

    %% Smart Authentication Routing
    AuthRouter --> AdminChannel
    AuthRouter --> CensusChannel
    AuthRouter --> PublicChannel

    %% Security Protection Flow
    AdminChannel --> SecurityGuard
    CensusChannel --> SecurityGuard
    PublicChannel --> SecurityGuard

    %% Endpoint Routing
    SecurityGuard --> AdminEndpoints
    SecurityGuard --> CensusEndpoints
    SecurityGuard --> PublicEndpoints

    %% Session Management
    AdminChannel --> AdminSessions
    CensusChannel --> CensusSessions
    AdminSessions --> SessionStore
    CensusSessions --> SessionStore

    %% Data Security
    AdminEndpoints --> AdminDB
    CensusEndpoints --> CensusDB
    SessionStore --> AdminDB
    SessionStore --> CensusDB

    %% =============================================================================
    %% BEAUTIFUL STYLING - Intuitive Authentication Colors
    %% =============================================================================

    classDef authJourney fill:#e8f4fd,stroke:#1565c0,stroke-width:3px,color:#000
    classDef authRouter fill:#fff3e0,stroke:#ef6c00,stroke-width:3px,color:#000
    classDef securitySystem fill:#ffebee,stroke:#d32f2f,stroke-width:3px,color:#000
    classDef sessionMgmt fill:#f3e5f5,stroke:#7b1fa2,stroke-width:3px,color:#000
    classDef dataVault fill:#f1f8e9,stroke:#558b2f,stroke-width:3px,color:#000

    %% Apply beautiful authentication styling
    class AdminLogin,CensusLogin,PublicAccess authJourney
    class AuthRouter,AdminChannel,CensusChannel,PublicChannel authRouter
    class SecurityGuard,AdminEndpoints,CensusEndpoints,PublicEndpoints securitySystem
    class AdminSessions,CensusSessions,SessionStore sessionMgmt
    class AdminDB,CensusDB dataVault
```

## Defense-in-Depth Security Flow

This diagram demonstrates our multi-layer security approach that prevents authentication conflicts and ensures system isolation:

```mermaid
sequenceDiagram
    participant 👨‍💼 as Admin User
    participant 🎯 as Smart Session Provider
    participant 🛡️ as Security Middleware
    participant 🔐 as API Endpoint
    participant ⚡ as NextAuth Engine

    Note over 👨‍💼,⚡: 🔒 Security Scenario: Admin User Protection

    rect rgb(240, 248, 255)
        Note over 👨‍💼,⚡: 🛡️ Layer 1: Smart Session Prevention
        👨‍💼->>🎯: 🌐 Navigate to homepage (/)
        🎯->>🎯: 🔍 Check for census-session-token
        🎯->>🎯: ❌ Cookie not found - provide null session
        Note over 🎯: ✅ SECURITY LAYER 1<br/>🚫 Prevents unnecessary session requests<br/>⚡ Performance optimized
    end

    rect rgb(255, 245, 245)
        Note over 👨‍💼,⚡: 🛡️ Layer 2: Cross-System Attack Prevention
        👨‍💼->>🛡️: 🚨 Hypothetical attack: /api/census/auth/session
        🛡️->>🛡️: 🔍 Detect admin-session-token cookie
        🛡️->>🛡️: ⚠️ Check against NEXTAUTH_SESSION_ENDPOINTS
        🛡️->>👨‍💼: 🚫 Return 400 AUTH_SYSTEM_MISMATCH
        Note over 🛡️: ✅ SECURITY LAYER 2<br/>🛡️ Blocks cross-authentication attacks<br/>🔒 System isolation enforced
    end

    rect rgb(245, 255, 245)
        Note over 👨‍💼,⚡: 🎉 Security Success Result
        Note over 👨‍💼,⚡: ✅ Zero CLIENT_FETCH_ERROR<br/>🎯 Clean User Experience<br/>🛡️ Attack Prevented<br/>⚡ Performance Maintained
    end
```

## Middleware Request Processing Flow

This technical diagram shows the complete middleware request processing pipeline with improved visual organization:

```mermaid
flowchart TD
    %% =============================================================================
    %% REQUEST ENTRY POINT
    %% =============================================================================
    Start([🚀 Incoming Request<br/>Next.js Middleware]) --> Static{📁 Static Asset<br/>Check?}

    %% =============================================================================
    %% STATIC ASSET OPTIMIZATION
    %% =============================================================================
    Static -->|✅ Static File| PassThrough[⚡ NextResponse.next<br/>Performance Optimized]
    Static -->|❌ Dynamic Request| AuthGuard[🛡️ Auth System Guard<br/>Security Checkpoint]

    %% =============================================================================
    %% SECURITY VALIDATION LAYER
    %% =============================================================================
    AuthGuard --> CheckCookies{🍪 Cookie State<br/>Validation}
    CheckCookies --> AdminToCensus{🚨 Admin Cookie +<br/>Census Endpoint?}
    CheckCookies --> CensusToAdmin{🚨 Census Cookie +<br/>Admin Endpoint?}
    CheckCookies --> ValidState[✅ Valid Cookie State<br/>Security Passed]

    AdminToCensus -->|🚫 Cross-Auth Attack| BlockRequest[🛑 Return 400<br/>AUTH_SYSTEM_MISMATCH]
    CensusToAdmin -->|🚫 Cross-Auth Attack| BlockRequest
    ValidState --> APIRoute{🔌 API Route<br/>Detection?}

    %% =============================================================================
    %% API PROCESSING BRANCH
    %% =============================================================================
    APIRoute -->|✅ API Request| APIAuth[🔐 API Authentication<br/>Handler]
    APIRoute -->|❌ Page Request| I18nRouting[🌍 i18n Routing<br/>Localization]

    APIAuth --> PublicAPI{🌍 Public API<br/>Check?}
    PublicAPI -->|✅ Public Access| PassThrough
    PublicAPI -->|❌ Protected API| AdminAPI{👨‍💼 Admin API<br/>Check?}

    AdminAPI -->|✅ Admin Route| AdminAuth[🔐 Admin Authentication<br/>Token Validation]
    AdminAPI -->|❌ Not Admin| CensusAPI{👥 Census API<br/>Check?}

    CensusAPI -->|✅ Census Route| CensusAuth[🔑 Census Authentication<br/>Code Validation]
    CensusAPI -->|❌ Unknown Route| PassThrough

    AdminAuth --> ValidAdmin{✅ Valid Admin<br/>Token?}
    ValidAdmin -->|✅ Authorized| PassThrough
    ValidAdmin -->|❌ Unauthorized| Return401[🚫 Return 401<br/>Unauthorized]

    CensusAuth --> ValidCensus{✅ Valid Census<br/>Token?}
    ValidCensus -->|✅ Valid Token| AccountDeleted{🗑️ Account<br/>Deleted?}
    ValidCensus -->|❌ Invalid Token| Return401

    AccountDeleted -->|✅ Deleted| Return403[🚫 Return 403<br/>Account Deleted]
    AccountDeleted -->|❌ Active| PassThrough

    %% =============================================================================
    %% PAGE PROCESSING BRANCH
    %% =============================================================================
    I18nRouting --> PublicPage{🌍 Public Page<br/>Check?}
    PublicPage -->|✅ Public Access| PassThrough
    PublicPage -->|❌ Protected Page| PageAuth[🔐 Page Authentication<br/>Handler]

    PageAuth --> AdminPage{👨‍💼 Admin Page<br/>Check?}
    AdminPage -->|✅ Admin Page| AdminPageAuth[🔐 Admin Page Auth<br/>Session Check]
    AdminPage -->|❌ Census Page| CensusPageAuth[🔑 Census Page Auth<br/>Session Check]

    AdminPageAuth --> ValidAdminPage{✅ Valid Admin<br/>Session?}
    ValidAdminPage -->|✅ Authorized| PassThrough
    ValidAdminPage -->|❌ Unauthorized| RedirectAdmin[🔄 Redirect to<br/>Admin Login]

    CensusPageAuth --> ValidCensusPage{✅ Valid Census<br/>Session?}
    ValidCensusPage -->|✅ Valid Session| CensusAccountDeleted{🗑️ Account<br/>Deleted?}
    ValidCensusPage -->|❌ Invalid Session| RedirectCensus[🔄 Redirect to<br/>Homepage]

    CensusAccountDeleted -->|✅ Deleted| RedirectDeleted[🔄 Redirect with<br/>Deletion Message]
    CensusAccountDeleted -->|❌ Active| PassThrough

    %% =============================================================================
    %% REQUEST COMPLETION
    %% =============================================================================
    PassThrough --> End([✅ Continue to Route<br/>Request Processed])
    BlockRequest --> End
    Return401 --> End
    Return403 --> End
    RedirectAdmin --> End
    RedirectCensus --> End
    RedirectDeleted --> End

    %% =============================================================================
    %% CLEAN TECHNICAL STYLING
    %% =============================================================================
    classDef startEnd fill:#e8f5e8,stroke:#4caf50,stroke-width:3px,color:#000
    classDef decision fill:#fff3e0,stroke:#ff9800,stroke-width:2px,color:#000
    classDef process fill:#e3f2fd,stroke:#2196f3,stroke-width:2px,color:#000
    classDef security fill:#f3e5f5,stroke:#9c27b0,stroke-width:2px,color:#000
    classDef error fill:#ffebee,stroke:#f44336,stroke-width:2px,color:#000
    classDef redirect fill:#e8f5e8,stroke:#607d8b,stroke-width:2px,color:#000

    class Start,End startEnd
    class Static,CheckCookies,AdminToCensus,CensusToAdmin,APIRoute,PublicAPI,AdminAPI,CensusAPI,ValidAdmin,ValidCensus,AccountDeleted,PublicPage,AdminPage,ValidAdminPage,ValidCensusPage,CensusAccountDeleted decision
    class APIAuth,I18nRouting,PageAuth,PassThrough process
    class AuthGuard,AdminAuth,CensusAuth,AdminPageAuth,CensusPageAuth security
    class BlockRequest,Return401,Return403 error
    class RedirectAdmin,RedirectCensus,RedirectDeleted redirect
```

## Session Management Architecture

This diagram shows how user sessions are intelligently managed throughout their journey, ensuring security and seamless experience:

```mermaid
flowchart TD
    %% =============================================================================
    %% USER SESSION EXPERIENCES - How Users Stay Connected
    %% =============================================================================
    subgraph "👤 User Session Experiences"
        direction TB

        subgraph "👨‍💼 Admin Session Experience"
            AdminExperience[👨‍💼 Admin Session<br/>🔐 High Security Mode<br/>⏰ 8-hour Duration<br/>🔄 Auto-renewal<br/>📱 2FA Protected]
        end

        subgraph "👥 Census Session Experience"
            CensusExperience[👥 Census Session<br/>🏠 Family Access Mode<br/>⏰ 8-hour Duration<br/>🛡️ Account Protection<br/>💾 Save & Continue]
        end

        subgraph "🌍 Public Session Experience"
            PublicExperience[🌍 Public Session<br/>ℹ️ Information Mode<br/>🚫 No Authentication<br/>⏱️ Rate Limited<br/>📊 Status Only]
        end
    end

    %% =============================================================================
    %% INTELLIGENT SESSION ROUTER - Smart Session Detection
    %% =============================================================================
    subgraph "🎯 Intelligent Session Router"
        direction TB

        SessionRouter[🎯 Smart Session Router<br/>🔍 Cookie Detection<br/>🚦 Context Routing<br/>⚡ Performance Optimized]

        subgraph "🔍 Session Decision Engine"
            direction LR
            SessionValidator[🔍 Session Validator<br/>Cookie Presence Check<br/>Context Verification<br/>Security Validation]

            SessionDecision{🤔 Valid Session<br/>Cookie Found?}

            NullMode[🚫 Null Session Mode<br/>No Network Requests<br/>Offline Capability<br/>Performance Optimized]

            ActiveMode[✅ Active Session Mode<br/>Full Authentication<br/>Real-time Updates<br/>Secure Operations]
        end
    end

    %% =============================================================================
    %% SECURE SESSION PROCESSING - JWT & Token Management
    %% =============================================================================
    subgraph "🔐 Secure Session Processing"
        direction TB

        subgraph "🎫 JWT Token Processing"
            direction LR
            AdminJWT[🔐 Admin JWT Engine<br/>Username + 2FA Claims<br/>Role-based Permissions<br/>8-hour Expiration]
            CensusJWT[🏠 Census JWT Engine<br/>Household Claims<br/>Account Deletion Checks<br/>8-hour Expiration]
        end

        subgraph "🔄 Session Lifecycle Management"
            direction LR
            TokenRefresh[🔄 Smart Token Refresh<br/>Automatic Renewal<br/>Seamless Experience<br/>Security Maintained]

            SessionValidation[✅ Session Validation<br/>Security Checks<br/>Expiration Handling<br/>Error Recovery]
        end
    end

    %% =============================================================================
    %% SESSION SECURITY VAULT - Protected Session Storage
    %% =============================================================================
    subgraph "🗄️ Session Security Vault"
        direction LR

        subgraph "🔒 Admin Session Vault"
            AdminVault[🔒 Admin Session Vault<br/>In-memory JWT Storage<br/>High Security Tokens<br/>Audit Trail]
        end

        subgraph "🏠 Census Session Vault"
            CensusVault[🏠 Census Session Vault<br/>In-memory JWT Storage<br/>Family Session Data<br/>Account Protection]
        end

        subgraph "⏱️ Performance Vault"
            PerformanceVault[⏱️ Performance Vault<br/>Rate Limiting Data<br/>Redis/Memory Store<br/>Optimization Metrics]
        end
    end

    %% =============================================================================
    %% BEAUTIFUL SESSION FLOWS - User Experience Connections
    %% =============================================================================

    %% User Session Experiences
    AdminExperience --> SessionRouter
    CensusExperience --> SessionRouter
    PublicExperience --> SessionRouter

    %% Smart Session Routing
    SessionRouter --> SessionValidator
    SessionValidator --> SessionDecision

    %% Session Decision Flow
    SessionDecision -->|❌ No Valid Cookie| NullMode
    SessionDecision -->|✅ Valid Cookie Found| ActiveMode

    %% Active Session Processing
    ActiveMode --> AdminJWT
    ActiveMode --> CensusJWT

    %% JWT Processing Flow
    AdminJWT --> TokenRefresh
    CensusJWT --> TokenRefresh
    TokenRefresh --> SessionValidation

    %% Session Storage
    SessionValidation --> AdminVault
    SessionValidation --> CensusVault
    SessionValidation --> PerformanceVault

    %% Performance Optimization
    NullMode --> PerformanceVault

    %% =============================================================================
    %% BEAUTIFUL STYLING - Intuitive Session Colors
    %% =============================================================================

    classDef sessionExperience fill:#e8f4fd,stroke:#1565c0,stroke-width:3px,color:#000
    classDef sessionRouter fill:#fff3e0,stroke:#ef6c00,stroke-width:3px,color:#000
    classDef sessionProcessing fill:#f3e5f5,stroke:#7b1fa2,stroke-width:3px,color:#000
    classDef sessionVault fill:#f1f8e9,stroke:#558b2f,stroke-width:3px,color:#000
    classDef decisionNode fill:#ffebee,stroke:#d32f2f,stroke-width:3px,color:#000

    %% Apply beautiful session styling
    class AdminExperience,CensusExperience,PublicExperience sessionExperience
    class SessionRouter,SessionValidator,NullMode,ActiveMode sessionRouter
    class AdminJWT,CensusJWT,TokenRefresh,SessionValidation sessionProcessing
    class AdminVault,CensusVault,PerformanceVault sessionVault
    class SessionDecision decisionNode
```

## Database Interaction Architecture

This technical diagram shows the complete database schema with improved visual organization and clear relationships:

```mermaid
erDiagram
    %% =============================================================================
    %% ADMIN SECURITY TABLES - Administrative Data
    %% =============================================================================
    ADMINS {
        int id PK "🔑 Primary Key"
        string username UK "👤 Unique Username"
        string passwordHash "🔐 Encrypted Password"
        string fullName "📝 Display Name"
        boolean totpEnabled "📱 2FA Status"
        string totpSecret "🔒 2FA Secret Key"
        datetime createdAt "📅 Creation Time"
        datetime updatedAt "🔄 Last Modified"
    }

    %% =============================================================================
    %% CENSUS MANAGEMENT TABLES - System Configuration
    %% =============================================================================
    CENSUS_YEARS {
        string id PK "🔑 Primary Key"
        int year UK "📅 Census Year"
        boolean isActive "✅ Active Status"
        datetime startDate "🚀 Start Date"
        datetime endDate "🏁 End Date"
        datetime createdAt "📅 Creation Time"
        datetime updatedAt "🔄 Last Modified"
    }

    UNIQUE_CODES {
        string id PK "🔑 Primary Key"
        string code UK "🎫 Access Code"
        string censusYearId FK "📅 Census Year Link"
        string householdId FK "🏠 Household Link"
        boolean isUsed "✅ Usage Status"
        datetime createdAt "📅 Creation Time"
        datetime usedAt "⏰ Usage Time"
    }

    %% =============================================================================
    %% FAMILY DATA TABLES - Census Information
    %% =============================================================================
    HOUSEHOLDS {
        string id PK "🔑 Primary Key"
        string uniqueCode UK "🎫 Access Code"
        string censusYearId FK "📅 Census Year Link"
        boolean isDeleted "🗑️ Deletion Status"
        datetime createdAt "📅 Creation Time"
        datetime updatedAt "🔄 Last Modified"
    }

    MEMBERS {
        string id PK "🔑 Primary Key"
        string householdId FK "🏠 Household Link"
        string firstName "👤 First Name"
        string lastName "👤 Last Name"
        date dateOfBirth "🎂 Birth Date"
        string relationship "👥 Family Role"
        datetime createdAt "📅 Creation Time"
        datetime updatedAt "🔄 Last Modified"
    }

    SACRAMENTS {
        string id PK "🔑 Primary Key"
        string memberId FK "👤 Member Link"
        string type "⛪ Sacrament Type"
        date dateReceived "📅 Ceremony Date"
        string location "📍 Church Location"
        datetime createdAt "📅 Creation Time"
        datetime updatedAt "🔄 Last Modified"
    }

    %% =============================================================================
    %% DATABASE RELATIONSHIPS - Data Connections
    %% =============================================================================

    %% Census Year Management
    CENSUS_YEARS ||--o{ HOUSEHOLDS : "📅 belongs_to_year"
    CENSUS_YEARS ||--o{ UNIQUE_CODES : "🎫 generated_for_year"

    %% Household Structure
    HOUSEHOLDS ||--|| UNIQUE_CODES : "🎫 assigned_code"
    HOUSEHOLDS ||--o{ MEMBERS : "👥 has_members"

    %% Member Records
    MEMBERS ||--o{ SACRAMENTS : "⛪ received_sacraments"
```

## Security Boundaries and Error Handling

This diagram shows our comprehensive security perimeter and intelligent error management system that protects users and maintains system integrity:

```mermaid
flowchart TD
    %% =============================================================================
    %% SECURITY PERIMETER - Protected System Boundaries
    %% =============================================================================
    subgraph "🛡️ Security Perimeter"
        direction TB

        subgraph "🔐 Admin Security Zone"
            AdminBoundary[🔐 Admin Security Boundary<br/>👨‍💼 Role-based Access<br/>📱 2FA Required<br/>🔒 High Security Mode<br/>⏰ 8-hour Sessions]
        end

        subgraph "🏠 Census Security Zone"
            CensusBoundary[🏠 Census Security Boundary<br/>🎫 Code-based Access<br/>👥 Family Protection<br/>🛡️ Account Deletion Checks<br/>💾 Session Persistence]
        end

        subgraph "🌍 Public Access Zone"
            PublicBoundary[🌍 Public Access Boundary<br/>ℹ️ Information Only<br/>⏱️ Rate Limited<br/>🔍 Status Monitoring<br/>🚫 No Authentication]
        end
    end

    %% =============================================================================
    %% INTELLIGENT ERROR MANAGEMENT - Graceful Error Handling
    %% =============================================================================
    subgraph "🚨 Intelligent Error Management"
        direction TB

        subgraph "⚠️ Error Classification"
            direction LR
            AuthErrors[🔐 Authentication Errors<br/>Login Failures<br/>Token Expiration<br/>Permission Denied]
            ValidationErrors[✅ Validation Errors<br/>Input Validation<br/>Data Format Issues<br/>Business Rules]
            SystemErrors[⚙️ System Errors<br/>Server Issues<br/>Database Problems<br/>Network Failures]
        end

        UserFeedback[📢 User Feedback System<br/>🌍 Multi-language Support<br/>🎯 Context-aware Messages<br/>🍪 Toast Notifications<br/>✨ Graceful UX]
    end

    %% =============================================================================
    %% SECURITY MONITORING - Threat Detection & Audit
    %% =============================================================================
    subgraph "📊 Security Monitoring & Audit"
        direction TB

        subgraph "📝 Intelligent Logging"
            direction LR
            DevLogging[🔧 Development Logging<br/>DEV Prefixed Messages<br/>Detailed Debug Info<br/>Local Development]
            ProdLogging[🏭 Production Logging<br/>Structured JSON Format<br/>Performance Optimized<br/>Security Focused]
        end

        subgraph "🔍 Security Intelligence"
            direction LR
            SecurityAudit[🔍 Security Audit Trail<br/>Access Attempts<br/>Permission Changes<br/>Threat Detection]
            PerformanceMetrics[📈 Performance Monitoring<br/>Response Times<br/>Error Rates<br/>System Health]
        end
    end

    %% =============================================================================
    %% THREAT PROTECTION - Advanced Security Measures
    %% =============================================================================
    subgraph "🛡️ Advanced Threat Protection"
        direction TB

        subgraph "⏱️ Progressive Defense"
            direction LR
            IPRateLimit[⏱️ IP Rate Limiting<br/>Request Throttling<br/>Abuse Prevention<br/>Fair Usage]
            ProgressiveTimeout[⏰ Progressive Timeout<br/>Escalating Delays<br/>Adaptive Response<br/>Smart Blocking]
        end

        subgraph "🔒 Session Security"
            direction LR
            SessionLocking[🔒 Session Locking<br/>Account Protection<br/>Concurrent Access<br/>Security Enforcement]
            BruteForceProtection[🛡️ Brute Force Shield<br/>Attack Detection<br/>Automatic Blocking<br/>Threat Mitigation]
        end
    end

    %% =============================================================================
    %% SECURITY FLOW CONNECTIONS - Integrated Protection
    %% =============================================================================

    %% Security Boundary to Error Management
    AdminBoundary --> AuthErrors
    CensusBoundary --> AuthErrors
    PublicBoundary --> ValidationErrors

    %% Error Classification to User Feedback
    AuthErrors --> UserFeedback
    ValidationErrors --> UserFeedback
    SystemErrors --> UserFeedback

    %% Error Monitoring Integration
    AuthErrors --> DevLogging
    AuthErrors --> ProdLogging
    ValidationErrors --> SecurityAudit
    SystemErrors --> PerformanceMetrics

    %% Progressive Threat Protection
    AuthErrors --> IPRateLimit
    IPRateLimit --> ProgressiveTimeout
    ProgressiveTimeout --> SessionLocking
    SessionLocking --> BruteForceProtection

    %% Cross-cutting Security Intelligence
    SecurityAudit --> BruteForceProtection
    PerformanceMetrics --> IPRateLimit

    %% =============================================================================
    %% BEAUTIFUL STYLING - Security-Focused Colors
    %% =============================================================================

    classDef securityZone fill:#ffebee,stroke:#d32f2f,stroke-width:3px,color:#000
    classDef errorMgmt fill:#fff3e0,stroke:#f57c00,stroke-width:3px,color:#000
    classDef monitoring fill:#e8f5e8,stroke:#388e3c,stroke-width:3px,color:#000
    classDef protection fill:#f3e5f5,stroke:#7b1fa2,stroke-width:3px,color:#000
    classDef feedback fill:#e3f2fd,stroke:#1976d2,stroke-width:3px,color:#000

    %% Apply security-focused styling
    class AdminBoundary,CensusBoundary,PublicBoundary securityZone
    class AuthErrors,ValidationErrors,SystemErrors errorMgmt
    class DevLogging,ProdLogging,SecurityAudit,PerformanceMetrics monitoring
    class IPRateLimit,ProgressiveTimeout,SessionLocking,BruteForceProtection protection
    class UserFeedback feedback
```

## Component Integration Summary

### Key Integration Points

1. **CombinedAuthProvider**: Pathname-based authentication provider selection for automatic context switching
2. **Full Page Navigation**: Cross-context navigation using `window.location.href` for clean transitions
3. **Middleware Guard**: Fallback defense with endpoint-level protection and structured error responses
4. **NextAuth.js Dual Configuration**: Separate secrets, cookies, and session management for complete isolation
5. **Database Separation**: Logical separation of admin and census data with proper foreign key relationships

### Performance Optimizations

- **Smart Navigation**: Client-side routing for same-context, full page refresh for cross-context
- **Efficient Provider Selection**: Pathname-based provider switching with zero overhead
- **Optimized Session Management**: Context-aware session provider selection
- **Minimal Network Impact**: Cross-context navigation adds ~1-2 seconds but eliminates errors
- **Optimized Database Queries**: Proper indexing and relationship management

### Security Features

- **Complete Cookie Isolation**: Separate namespaces prevent cross-contamination
- **Defense-in-Depth**: Multiple layers of protection against authentication conflicts
- **Cross-Context Navigation Security**: Full page refresh prevents session cookie conflicts
- **Zero CLIENT_FETCH_ERROR**: Eliminated through comprehensive navigation strategy
- **Account Deletion Security**: Proper handling of deleted accounts with secure redirects
- **Rate Limiting**: Progressive timeout system for brute force protection
- **Audit Logging**: Comprehensive security event tracking

### Scalability Considerations

- **Modular Architecture**: Easy to extend with additional authentication systems
- **Stateless Design**: JWT-based sessions for horizontal scaling
- **Database Optimization**: Proper indexing and query optimization
- **Caching Strategy**: Intelligent session and data caching
- **Performance Monitoring**: Built-in metrics and logging for optimization

## Complete System Overview Diagram

The following master diagram provides an intuitive, beautiful overview of the entire WSCCC Census System architecture, focusing on user journeys and core system flows:

```mermaid
flowchart TD
    %% =============================================================================
    %% USER ENTRY POINTS - How Users Access the System
    %% =============================================================================
    subgraph "🌐 User Entry Points"
        direction LR
        AdminUser[👨‍💼 Admin User<br/>System Administrator]
        CensusUser[👥 Census Participant<br/>Family Representative]
        PublicUser[🌍 Public Visitor<br/>Information Seeker]
    end

    %% =============================================================================
    %% DUAL PORTAL INTERFACES - User Experience Layers
    %% =============================================================================
    subgraph "🖥️ Portal Interfaces"
        direction LR

        subgraph "Admin Experience"
            AdminPortal[👨‍💼 Admin Portal<br/>🔐 Secure Management<br/>🤖 AI Analytics<br/>📊 Data Control]
        end

        subgraph "Participant Experience"
            CensusPortal[👥 Census Portal<br/>🔑 Code Entry<br/>🏠 Family Registration<br/>📱 Mobile Friendly]
        end

        subgraph "Public Experience"
            PublicPortal[🌍 Public Portal<br/>ℹ️ Information<br/>📊 Status Check<br/>🔍 Rate Limits]
        end
    end

    %% =============================================================================
    %% SMART AUTHENTICATION - Context-Aware Security
    %% =============================================================================
    subgraph "🔐 Smart Authentication System"
        direction TB

        AuthRouter[🎯 Authentication Router<br/>Intelligent Context Detection]

        subgraph "Dual Security Channels"
            direction LR
            AdminChannel[🔒 Admin Channel<br/>Username + 2FA<br/>admin-session-token]
            CensusChannel[🔑 Census Channel<br/>Unique Code Access<br/>census-session-token]
        end

        SecurityGuard[🛡️ Security Guard<br/>Cross-System Prevention<br/>Rate Limiting<br/>Session Isolation]
    end

    %% =============================================================================
    %% FEATURE ECOSYSTEMS - Core Capabilities
    %% =============================================================================
    subgraph "⚙️ Admin Feature Ecosystem"
        direction TB

        subgraph "👥 People Management"
            UserMgmt[👥 User Management<br/>Admin Accounts<br/>Role Control]
            FamilyMgmt[🏠 Family Management<br/>Household CRUD<br/>Member Records]
        end

        subgraph "🤖 Intelligence Hub"
            AISystem[🤖 AI Analytics<br/>Natural Language Queries<br/>Google Gemini 2.0]
            DataViz[📊 Smart Insights<br/>Charts & Reports<br/>Export Tools]
        end

        subgraph "⚙️ System Control"
            CensusAdmin[📊 Census Control<br/>Year Management<br/>Status Control]
            CodeSystem[🎲 Code System<br/>Generation & Tracking<br/>Security Validation]
        end
    end

    subgraph "👥 Census Feature Ecosystem"
        direction TB

        subgraph "🎯 Guided Journey"
            AccessFlow[🔑 Secure Access<br/>Code Validation<br/>Account Protection]
            OnboardFlow[🎯 Smart Onboarding<br/>Step-by-step Guide<br/>Progress Tracking]
        end

        subgraph "🏠 Family Builder"
            HouseholdFlow[🏠 Household Setup<br/>Family Information<br/>Head of House]
            MemberFlow[👤 Member Addition<br/>Individual Profiles<br/>Demographics]
        end

        subgraph "⛪ Records Management"
            SacramentFlow[⛪ Sacrament Records<br/>Religious Ceremonies<br/>Date Tracking]
            SessionFlow[💾 Session Management<br/>Save & Continue<br/>Multi-device Support]
        end
    end

    %% =============================================================================
    %% DATA FOUNDATION - Information Architecture
    %% =============================================================================
    subgraph "🗄️ Data Foundation"
        direction LR

        subgraph "👨‍💼 Admin Data"
            AdminDB[(🔐 Admin Database<br/>User Credentials<br/>Security Audit<br/>System Settings)]
        end

        subgraph "👥 Census Data"
            CensusDB[(🏠 Census Database<br/>Households & Members<br/>Sacrament Records<br/>Unique Codes)]
        end

        subgraph "📊 Intelligence Data"
            AnalyticsDB[(🤖 Analytics Store<br/>Query History<br/>Usage Patterns<br/>Performance Metrics)]
        end
    end

    %% =============================================================================
    %% BEAUTIFUL USER FLOWS - Intuitive Connections
    %% =============================================================================

    %% User Entry Flow
    AdminUser --> AdminPortal
    CensusUser --> CensusPortal
    PublicUser --> PublicPortal

    %% Portal to Authentication
    AdminPortal --> AuthRouter
    CensusPortal --> AuthRouter
    PublicPortal --> AuthRouter

    %% Authentication Routing
    AuthRouter --> AdminChannel
    AuthRouter --> CensusChannel
    AuthRouter --> SecurityGuard

    %% Admin Feature Flow
    AdminChannel --> UserMgmt
    AdminChannel --> FamilyMgmt
    AdminChannel --> AISystem
    AdminChannel --> CensusAdmin

    %% AI Intelligence Flow
    AISystem --> DataViz
    CensusAdmin --> CodeSystem

    %% Census Feature Flow
    CensusChannel --> AccessFlow
    AccessFlow --> OnboardFlow
    OnboardFlow --> HouseholdFlow
    HouseholdFlow --> MemberFlow
    MemberFlow --> SacramentFlow
    SacramentFlow --> SessionFlow

    %% Data Connections
    UserMgmt --> AdminDB
    FamilyMgmt --> CensusDB
    AISystem --> AnalyticsDB
    AISystem --> CensusDB
    CodeSystem --> CensusDB

    AccessFlow --> CensusDB
    HouseholdFlow --> CensusDB
    MemberFlow --> CensusDB
    SacramentFlow --> CensusDB

    %% Cross-cutting Connections
    SecurityGuard --> AdminDB
    SecurityGuard --> CensusDB
    DataViz --> AnalyticsDB

    %% =============================================================================
    %% BEAUTIFUL STYLING - Intuitive Color System
    %% =============================================================================

    classDef userEntry fill:#e8f4fd,stroke:#1565c0,stroke-width:3px,color:#000
    classDef portalInterface fill:#f3e5f5,stroke:#7b1fa2,stroke-width:3px,color:#000
    classDef authentication fill:#fff3e0,stroke:#ef6c00,stroke-width:3px,color:#000
    classDef adminEcosystem fill:#fce4ec,stroke:#c2185b,stroke-width:3px,color:#000
    classDef censusEcosystem fill:#e0f2f1,stroke:#00695c,stroke-width:3px,color:#000
    classDef dataFoundation fill:#f1f8e9,stroke:#558b2f,stroke-width:3px,color:#000

    %% Apply intuitive styling
    class AdminUser,CensusUser,PublicUser userEntry
    class AdminPortal,CensusPortal,PublicPortal portalInterface
    class AuthRouter,AdminChannel,CensusChannel,SecurityGuard authentication
    class UserMgmt,FamilyMgmt,AISystem,DataViz,CensusAdmin,CodeSystem adminEcosystem
    class AccessFlow,OnboardFlow,HouseholdFlow,MemberFlow,SacramentFlow,SessionFlow censusEcosystem
    class AdminDB,CensusDB,AnalyticsDB dataFoundation
```

This comprehensive architecture ensures enterprise-grade security, performance, and maintainability while providing a seamless user experience across both admin and census participant interfaces.
