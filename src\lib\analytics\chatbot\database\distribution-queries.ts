/**
 * Distribution Queries Module - Main Index
 *
 * This module contains the main distribution table generator and exports
 * all distribution query functions from their respective domain modules.
 */

import { logSecureError } from '@/lib/analytics/chatbot/security';
import type { DistributionTableData, QueryIntent } from '@/types/analytics';
import {
  getRelationshipDistributionTable,
  getSuburbDistributionTable,
} from './household-distributions';
// Import and re-export distribution functions from domain-specific modules
import {
  getAgeDistributionTable,
  getGenderDistributionTable,
  getHobbyDistributionTable,
  getOccupationDistributionTable,
} from './member-distributions';
import { getSacramentDistributionTable } from './sacrament-distributions';

// Re-export all distribution functions
export {
  getHobbyDistributionTable,
  getOccupationDistributionTable,
  getGenderDistributionTable,
  getAgeDistributionTable,
  getRelationshipDistributionTable,
  getSuburbDistributionTable,
  getSacramentDistributionTable,
};

// Universal distribution table generator
export async function generateDistributionTable(
  intent: QueryIntent
): Promise<DistributionTableData | null> {
  const { dataType, filters } = intent;

  try {
    switch (dataType) {
      case 'member_demographics':
        if (filters?.hobby) {
          return await getHobbyDistributionTable(filters);
        }
        if (filters?.occupation) {
          return await getOccupationDistributionTable(filters);
        }
        if (filters?.gender) {
          return await getGenderDistributionTable();
        }
        return await getAgeDistributionTable();

      case 'household_info':
        if (filters?.relationship) {
          return await getRelationshipDistributionTable();
        }
        return await getSuburbDistributionTable();

      case 'sacrament_records':
        return await getSacramentDistributionTable();

      default:
        return null;
    }
  } catch (error) {
    logSecureError('distribution_table_generation', error);
    return null;
  }
}
