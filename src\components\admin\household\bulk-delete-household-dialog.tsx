'use client';

import {
  <PERSON>ert<PERSON><PERSON>cle,
  AlertTriangle,
  CheckCircle,
  Home,
  Info,
  Trash2,
  Users,
  XCircle,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useCallback, useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
} from '@/components/ui/drawer';
import { useIsMobile } from '@/hooks/use-mobile';
import { useMessage } from '@/hooks/useMessage';

interface BulkDeleteHouseholdDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  householdIds: number[];
  onHouseholdsDeleted: (message?: string) => void;
}

interface ValidationResult {
  canProceed: boolean;
  totalHouseholds: number;
  deletableCount: number;
  undeletableCount: number;
  deletableHouseholds: Array<{
    householdId: number;
    canDelete: boolean;
    reason: string;
    deleteType: string;
    memberCount: number;
    householdHead: { name: string; id: number } | null;
  }>;
  undeletableHouseholds: Array<{
    householdId: number;
    canDelete: boolean;
    reason: string;
    deleteType: string;
    memberCount?: number;
    nonHeadMemberCount?: number;
    householdHead: { name: string; id: number } | null;
  }>;
  summary: {
    total: number;
    deletable: number;
    undeletable: number;
    withMembers: number;
    notFound: number;
    errors: number;
  };
}

export function BulkDeleteHouseholdDialog({
  open,
  onOpenChange,
  householdIds,
  onHouseholdsDeleted,
}: BulkDeleteHouseholdDialogProps) {
  const t = useTranslations();
  const tDialogs = useTranslations('dialogs');
  const tCommon = useTranslations('common');
  const tNotifications = useTranslations('notifications');
  const { showError, showAlert } = useMessage();
  const isMobile = useIsMobile();
  const [validation, setValidation] = useState<ValidationResult | null>(null);
  const [loading, setLoading] = useState(true);
  const [isDeleting, setIsDeleting] = useState(false);

  const fetchValidation = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch(
        '/api/admin/households/bulk-delete-validation',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ householdIds }),
        }
      );

      if (!response.ok) {
        throw new Error(t('errors.failedToValidateBulkDelete'));
      }

      const data = await response.json();
      setValidation(data);
    } catch (error) {
      console.error('Error validating bulk delete:', error);
      showError('ValidationFailed');
      onOpenChange(false);
    } finally {
      setLoading(false);
    }
  }, [householdIds, showError, onOpenChange, t]);

  // Fetch validation info when dialog opens
  useEffect(() => {
    if (open && householdIds.length > 0) {
      fetchValidation();
    }
  }, [open, householdIds, fetchValidation]);

  const handleBulkDelete = async () => {
    if (!validation?.canProceed) {
      return;
    }

    try {
      setIsDeleting(true);

      // Only delete the households that can be deleted
      const deletableIds = validation.deletableHouseholds.map(
        (h) => h.householdId
      );

      const response = await fetch('/api/admin/households/bulk-delete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ householdIds: deletableIds }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.error || t('common.failedToDeleteHouseholds')
        );
      }

      await response.json();

      // Show success message using centralized alert system with proper next-intl parameter passing
      const deletedCount = deletableIds.length;
      const skippedCount = validation.undeletableCount;

      if (skippedCount > 0) {
        // Use proper next-intl parameter passing (TypeScript definitions now fixed)
        const message = tNotifications('bulkDeletePartialSuccess', {
          deletedCount: deletedCount.toString(),
          skippedCount: skippedCount.toString(),
        });
        showAlert('success', message);
      } else {
        // Use proper next-intl parameter passing (TypeScript definitions now fixed)
        const message = tNotifications('bulkDeleteSuccess', {
          count: deletedCount.toString(),
        });
        showAlert('success', message);
      }

      onOpenChange(false);
      onHouseholdsDeleted(); // No message parameter needed - handled by centralized system
    } catch (error) {
      console.error('Error deleting households:', error);
      showError('failedToDeleteHouseholds');
    } finally {
      setIsDeleting(false);
    }
  };

  const getDialogContent = () => {
    if (loading) {
      return (
        <div className="py-6 text-center">
          <div className="mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent" />
          <p className="text-muted-foreground">
            {t('common.validatingHouseholdsForDeletio')}
          </p>
        </div>
      );
    }

    if (!validation) {
      return (
        <div className="py-6 text-center">
          <AlertCircle className="mx-auto mb-4 h-8 w-8 text-destructive" />
          <p className="text-muted-foreground">
            {t('common.failedToValidateHouseholds')}
          </p>
        </div>
      );
    }

    return (
      <div className="space-y-4">
        {/* Summary */}
        <div className="grid grid-cols-3 gap-4 rounded-lg bg-muted/20 p-4">
          <div className="text-center">
            <div className="font-bold text-2xl">{validation.summary.total}</div>
            <div className="text-muted-foreground text-sm">
              {tCommon('total')}
            </div>
          </div>
          <div className="text-center">
            <div className="font-bold text-2xl text-green-600">
              {validation.summary.deletable}
            </div>
            <div className="text-muted-foreground text-sm">
              {tCommon('deletable')}
            </div>
          </div>
          <div className="text-center">
            <div className="font-bold text-2xl text-red-600">
              {validation.summary.undeletable}
            </div>
            <div className="text-muted-foreground text-sm">
              {t('common.cannotDelete')}
            </div>
          </div>
        </div>

        {/* Warnings for undeletable households */}
        {validation.undeletableCount > 0 && (
          <div className="space-y-3">
            <div className="flex items-center gap-2 text-orange-600 dark:text-orange-400">
              <AlertTriangle className="h-4 w-4" />
              <span className="font-medium">
                {tDialogs('cannotDeleteCount', {
                  count: validation.undeletableCount.toString(),
                })}
              </span>
            </div>

            {validation.summary.withMembers > 0 && (
              <div className="rounded-lg border border-orange-200 bg-orange-50 p-3 dark:border-orange-800 dark:bg-orange-950/20">
                <div className="mb-2 flex items-center gap-2 text-orange-800 dark:text-orange-200">
                  <Users className="h-4 w-4" />
                  <span className="font-medium">
                    {tDialogs('householdsWithMembers', {
                      count: validation.summary.withMembers.toString(),
                    })}
                  </span>
                </div>
                <p className="text-orange-700 text-sm dark:text-orange-300">
                  {tDialogs('householdsWithMembersWarning')}
                </p>
              </div>
            )}

            {(validation.summary.notFound > 0 ||
              validation.summary.errors > 0) && (
              <div className="rounded-lg border border-red-200 bg-red-50 p-3 dark:border-red-800 dark:bg-red-950/20">
                <div className="mb-2 flex items-center gap-2 text-red-800 dark:text-red-200">
                  <XCircle className="h-4 w-4" />
                  <span className="font-medium">
                    {tDialogs('householdsUnavailable', {
                      count: (
                        validation.summary.notFound + validation.summary.errors
                      ).toString(),
                    })}
                  </span>
                </div>
                <p className="text-red-700 text-sm dark:text-red-300">
                  {tDialogs('householdsNotFoundWarning')}
                </p>
              </div>
            )}
          </div>
        )}

        {/* Deletable households */}
        {validation.deletableCount > 0 && (
          <div className="space-y-3">
            <div className="flex items-center gap-2 text-green-600 dark:text-green-400">
              <CheckCircle className="h-4 w-4" />
              <span className="font-medium">
                {tDialogs('willDeleteCount', {
                  count: validation.deletableCount.toString(),
                })}
              </span>
            </div>

            <div className="rounded-lg border border-green-200 bg-green-50 p-3 dark:border-green-800 dark:bg-green-950/20">
              <div className="space-y-2 text-sm">
                <div className="flex items-center gap-2 text-green-700 dark:text-green-300">
                  <Home className="h-4 w-4" />
                  <span>{t('common.householdInformationWillBeDele')}</span>
                </div>
                <div className="flex items-center gap-2 text-green-700 dark:text-green-300">
                  <Users className="h-4 w-4" />
                  <span>{t('common.householdHeadDetailsWillBeRemo')}</span>
                </div>
                <div className="flex items-center gap-2 text-green-700 dark:text-green-300">
                  <Info className="h-4 w-4" />
                  <span>{t('common.associatedUniqueCodesWillBeMar')}</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {!validation.canProceed && (
          <div className="rounded-lg border border-destructive/20 bg-destructive/10 p-4">
            <p className="font-medium text-destructive text-sm">
              {tDialogs('noHouseholdsCanBeDeleted')}
            </p>
          </div>
        )}
      </div>
    );
  };

  const getActionButtons = () => {
    if (loading || !validation) {
      return (
        <Button onClick={() => onOpenChange(false)} variant="outline">
          {tCommon('cancel')}
        </Button>
      );
    }

    if (!validation.canProceed) {
      return (
        <Button onClick={() => onOpenChange(false)} variant="outline">
          {tCommon('close')}
        </Button>
      );
    }

    return (
      <>
        <Button onClick={() => onOpenChange(false)} variant="outline">
          {tCommon('cancel')}
        </Button>
        <Button
          className="flex items-center gap-2"
          disabled={isDeleting}
          onClick={handleBulkDelete}
          variant="destructive"
        >
          {isDeleting ? (
            <>
              <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
              {tCommon('deleting')}
            </>
          ) : (
            <>
              <Trash2 className="h-4 w-4" />
              {tDialogs('deleteCountHouseholds', {
                count: validation.deletableCount.toString(),
              })}
            </>
          )}
        </Button>
      </>
    );
  };

  // Mobile implementation using Drawer (native swipe-to-dismiss from bottom)
  // Features: Visual handle bar, smooth swipe gestures, and fully scrollable content including titles
  if (isMobile) {
    return (
      <Drawer onOpenChange={onOpenChange} open={open}>
        <DrawerContent className="flex max-h-[80dvh] min-h-[60svh] flex-col">
          <DrawerHeader className="sr-only">
            <DrawerTitle>{t('common.deleteSelectedHouseholds')}</DrawerTitle>
            <DrawerDescription>
              {validation?.canProceed
                ? tDialogs('reviewHouseholdsForDeletion')
                : tDialogs('someHouseholdsCannotBeDeleted')}
            </DrawerDescription>
          </DrawerHeader>
          <div className="scrollbar-hide flex-1 overflow-y-auto">
            <div className="flex flex-col gap-1.5 p-4">
              <h2 className="font-semibold text-lg leading-none tracking-tight">
                {t('common.deleteSelectedHouseholds')}
              </h2>
              <p className="text-muted-foreground text-sm">
                {validation?.canProceed
                  ? tDialogs('reviewHouseholdsForDeletion')
                  : tDialogs('someHouseholdsCannotBeDeleted')}
              </p>
            </div>
            <div className="px-4 pb-4">
              {getDialogContent()}

              <div className="mt-6 pt-4">
                <div className="flex justify-end gap-2">
                  {getActionButtons()}
                </div>
              </div>
            </div>
          </div>
        </DrawerContent>
      </Drawer>
    );
  }

  // Desktop implementation using Dialog (unchanged)
  return (
    <Dialog onOpenChange={onOpenChange} open={open}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>{t('common.deleteSelectedHouseholds')}</DialogTitle>
          <DialogDescription>
            {validation?.canProceed
              ? tDialogs('reviewHouseholdsForDeletion')
              : tDialogs('someHouseholdsCannotBeDeleted')}
          </DialogDescription>
        </DialogHeader>

        {getDialogContent()}

        <DialogFooter className="flex gap-2">{getActionButtons()}</DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
