'use client';

import {
  ChevronDown,
  FileText,
  Home,
  LogOut,
  Menu,
  UserCircle,
  X,
} from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useEffect, useMemo, useState } from 'react';
import { LiquidProgressCircle } from '@/components/census/liquid-progress-circle';
import { CensusWelcomeModal } from '@/components/census/welcome-modal/CensusWelcomeModal';
import { useWelcomeModal } from '@/components/census/welcome-modal/useWelcomeModal';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useCensusProgress } from '@/hooks/use-census-progress';
import { useCensusAuth } from '@/hooks/useCensusAuth';
import { cn } from '@/lib/utils';

/**
 * CensusHeader component - Provides navigation and authentication controls for the census portal
 *
 * Features:
 * - Responsive design with different layouts for mobile and desktop
 * - Authentication status display and sign out functionality
 * - Conditional help icon for second stage (after household registration)
 * - Household information display
 */
interface CensusHeaderProps {
  isSecondStage?: boolean; // True when user is in the census form stage (after household registration)
}

export function CensusHeader({ isSecondStage = false }: CensusHeaderProps) {
  const t = useTranslations('navigation');
  const tCommon = useTranslations('common');
  const tAdmin = useTranslations('admin');
  const tBrand = useTranslations('brand');

  const pathname = usePathname();
  const { signOutFromCensus, isAuthenticated, isLoading, session } =
    useCensusAuth();

  // Determine if progress badge or help icon should be shown
  const shouldShowProgressElements = useMemo(() => {
    try {
      // Server-side determination (works for returning users)
      const serverSideSecondStage =
        isSecondStage && pathname && !pathname.includes('/account');

      // Client-side determination (works for first-time users after registration)
      const clientSideSecondStage =
        isAuthenticated &&
        session?.user?.householdId &&
        pathname &&
        !pathname.includes('/account');

      // Show progress elements if either server-side OR client-side indicates second stage
      return serverSideSecondStage || clientSideSecondStage;
    } catch (error) {
      // Fail safely - don't show progress elements if there's any error
      if (process.env.NODE_ENV === 'development') {
        console.error('Error determining progress elements visibility:', error);
      }
      return false;
    }
  }, [isSecondStage, pathname, isAuthenticated, session?.user?.householdId]);

  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [dropdownOpen, setDropdownOpen] = useState(false);

  // Welcome modal state for help functionality
  const {
    isOpen: isWelcomeOpen,
    setIsOpen: setWelcomeOpen,
    dismissModal,
  } = useWelcomeModal();

  // Progress tracking for progress badge with loading state
  // Now uses global state management - no manual refresh needed!
  const { progress, isLoading: isProgressLoading } = useCensusProgress();

  // Always show progress badge when progress elements should be visible
  const shouldShowProgressBadge = shouldShowProgressElements;

  /**
   * Handle user logout with loading state
   */
  const handleLogout = async () => {
    setIsLoggingOut(true);
    try {
      await signOutFromCensus();
    } catch (error) {
      console.error('Error logging out:', error);
    }
  };

  /**
   * Handle help icon click - opens the welcome modal
   */
  const handleHelpClick = () => {
    setWelcomeOpen(true);
  };

  /**
   * Handle responsive behavior
   * - Closes dropdown menu when screen is resized to mobile view
   * - Uses window resize event listener with proper cleanup
   */
  useEffect(() => {
    const handleResize = () => {
      const isMobile = window.innerWidth < 768; // md breakpoint is 768px

      // Close dropdown if screen is resized to mobile
      if (isMobile && dropdownOpen) {
        setDropdownOpen(false);
      }
    };

    // Initial check
    handleResize();

    // Add resize listener
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, [dropdownOpen]);

  /**
   * Coordinate mobile menu and dropdown menu states
   * - Ensures they don't conflict with each other
   */
  useEffect(() => {
    if (dropdownOpen && mobileMenuOpen) {
      setMobileMenuOpen(false);
    }
  }, [dropdownOpen, mobileMenuOpen]);

  /**
   * Toggle mobile menu open/closed
   */
  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  /**
   * Close mobile menu when clicking outside
   */
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Only run this if the mobile menu is open
      if (!mobileMenuOpen) {
        return;
      }

      // Check if the click was outside the header
      const header = document.querySelector('header');
      if (header && !header.contains(event.target as Node)) {
        setMobileMenuOpen(false);
      }
    };

    // Add event listener
    document.addEventListener('mousedown', handleClickOutside);

    // Cleanup
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [mobileMenuOpen]);

  /**
   * Render the CensusHeader component
   */
  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/80 backdrop-blur-sm supports-[backdrop-filter]:bg-background/60">
      <div className="flex h-16 items-center justify-between px-0">
        {/* Logo and Brand */}
        <div className="flex items-center gap-2 pl-4">
          <Link
            className="flex cursor-pointer items-center gap-2 font-medium"
            href="/"
          >
            <div className="flex h-6 w-6 items-center justify-center rounded-md bg-primary text-primary-foreground">
              <svg
                aria-hidden="true"
                fill="none"
                height="16"
                stroke="currentColor"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                viewBox="0 0 24 24"
                width="16"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M3 9h18v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9Z" />
                <path d="M3 9V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v4" />
                <path d="M13 13h4" />
                <path d="M13 17h4" />
                <path d="M7 13h2v4H7z" />
              </svg>
            </div>
            <span className="font-medium sm:inline-block">
              {tBrand('name')}
            </span>
          </Link>
        </div>

        {/* Desktop Navigation */}
        <nav
          aria-label={tAdmin('mainNavigation')}
          className="hidden items-center gap-6 md:flex"
        >
          <Link
            className={cn(
              'flex cursor-pointer items-center justify-center rounded-md p-2 font-medium text-sm transition-colors hover:bg-accent',
              pathname === '/' ? 'text-primary' : 'text-muted-foreground'
            )}
            href="/"
          >
            <Home aria-hidden="true" className="mr-2 h-4 w-4" />
            {t('home')}
          </Link>
          {isAuthenticated && session?.user?.code && (
            <>
              <Link
                className={cn(
                  'flex cursor-pointer items-center justify-center rounded-md p-2 font-medium text-sm transition-colors hover:bg-accent',
                  pathname === `/census/${session.user.code}`
                    ? 'text-primary'
                    : 'text-muted-foreground'
                )}
                href={`/census/${session.user.code}`}
              >
                <FileText aria-hidden="true" className="mr-2 h-4 w-4" />
                {t('census')}
              </Link>
              <Link
                className={cn(
                  'flex cursor-pointer items-center justify-center rounded-md p-2 font-medium text-sm transition-colors hover:bg-accent',
                  pathname?.includes('/account')
                    ? 'text-primary'
                    : 'text-muted-foreground'
                )}
                href={`/census/${session.user.code}/account`}
              >
                <UserCircle aria-hidden="true" className="mr-2 h-4 w-4" />
                {t('account')}
              </Link>
            </>
          )}
        </nav>

        {/* Right Side Actions */}
        <div className="flex items-center gap-2 pr-4">
          {/* Liquid Progress Circle - Always show when in second stage */}
          {shouldShowProgressBadge && (
            <LiquidProgressCircle
              className={
                isProgressLoading
                  ? 'opacity-75 transition-opacity duration-300'
                  : ''
              }
              onClick={handleHelpClick}
              progress={progress}
            />
          )}

          {/* Auth Button with Loading State - Only visible on medium screens and larger */}
          {isAuthenticated && (
            <div className="hidden md:block">
              <DropdownMenu onOpenChange={setDropdownOpen} open={dropdownOpen}>
                <DropdownMenuTrigger asChild>
                  <Button
                    className="cursor-pointer gap-1"
                    size="sm"
                    variant="ghost"
                  >
                    {isLoading ? (
                      <>
                        <span>{tCommon('loading')}</span>
                        <div
                          aria-hidden="true"
                          className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"
                        />
                      </>
                    ) : (
                      <>
                        <span>{session?.user?.name || t('household')}</span>
                        <ChevronDown aria-hidden="true" className="h-4 w-4" />
                      </>
                    )}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {isLoading ? (
                    <DropdownMenuItem className="font-medium" disabled>
                      <div className="flex w-full items-center justify-center py-1">
                        <div
                          aria-hidden="true"
                          className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"
                        />
                        <span>{t('loadingHouseholdData')}</span>
                      </div>
                    </DropdownMenuItem>
                  ) : (
                    <>
                      <DropdownMenuItem className="font-medium" disabled>
                        {t('code')}: {session?.user?.code}
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      {/* Custom styling to ensure the dropdown item doesn't affect the button's hover state */}
                      <div className="px-2 py-1.5">
                        <Button
                          className="w-full cursor-pointer justify-start"
                          disabled={isLoggingOut}
                          onClick={handleLogout}
                          size="sm"
                          variant="destructive"
                        >
                          <LogOut aria-hidden="true" className="mr-2 h-4 w-4" />
                          <span>
                            {isLoggingOut ? t('signingOut') : t('signOut')}
                          </span>
                        </Button>
                      </div>
                    </>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          )}

          {/* Mobile Menu Button */}
          <Button
            aria-expanded={mobileMenuOpen}
            aria-label={tCommon('toggleMobileMenu')}
            className="cursor-pointer md:hidden"
            onClick={toggleMobileMenu}
            size="icon"
            variant="ghost"
          >
            {mobileMenuOpen ? (
              <X aria-hidden="true" className="h-5 w-5" />
            ) : (
              <Menu aria-hidden="true" className="h-5 w-5" />
            )}
            <span className="sr-only">{t('toggleMenu')}</span>
          </Button>
        </div>
      </div>

      {/* Mobile Navigation - Overlay Style */}
      {mobileMenuOpen && (
        <>
          {/* Backdrop */}
          <div className="fade-in-0 fixed inset-0 top-16 z-40 animate-in bg-background/80 backdrop-blur-sm duration-100" />

          {/* Menu */}
          <div className="slide-in-from-top-5 fixed inset-x-0 top-16 z-50 animate-in border-t bg-background/95 shadow-lg backdrop-blur-sm duration-200 md:hidden">
            <nav
              aria-label={tAdmin('mobileNavigation')}
              className="flex max-h-[calc(100vh-4rem)] flex-col space-y-4 overflow-y-auto p-4"
            >
              <Link
                className={cn(
                  'flex cursor-pointer items-center gap-2 rounded-md p-2 font-medium text-sm transition-colors hover:bg-accent',
                  pathname === '/'
                    ? 'bg-muted/50 text-primary'
                    : 'text-foreground'
                )}
                href="/"
                onClick={() => setMobileMenuOpen(false)}
              >
                <Home aria-hidden="true" className="h-4 w-4" />
                {t('home')}
              </Link>
              {isAuthenticated && session?.user?.code && (
                <>
                  <Link
                    className={cn(
                      'flex cursor-pointer items-center gap-2 rounded-md p-2 font-medium text-sm transition-colors hover:bg-accent',
                      pathname === `/census/${session.user.code}`
                        ? 'bg-muted/50 text-primary'
                        : 'text-foreground'
                    )}
                    href={`/census/${session.user.code}`}
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    <FileText aria-hidden="true" className="h-4 w-4" />
                    {t('census')}
                  </Link>
                  <Link
                    className={cn(
                      'flex cursor-pointer items-center gap-2 rounded-md p-2 font-medium text-sm transition-colors hover:bg-accent',
                      pathname?.includes('/account')
                        ? 'bg-muted/50 text-primary'
                        : 'text-foreground'
                    )}
                    href={`/census/${session.user.code}/account`}
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    <UserCircle aria-hidden="true" className="h-4 w-4" />
                    {t('account')}
                  </Link>
                </>
              )}

              {/* Mobile Household Information and Logout */}
              {isAuthenticated && (
                <>
                  {/* Household Information */}
                  {!isLoading && (
                    <div className="mb-2 rounded-md bg-accent/50 p-3">
                      <div className="flex flex-col space-y-1">
                        <div className="font-medium text-sm">
                          {session?.user?.name || t('household')}
                        </div>
                        <div className="text-muted-foreground text-xs">
                          {t('code')}: {session?.user?.code}
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Mobile Logout Button with Loading State */}
                  {isLoading ? (
                    <Button
                      className="mt-2"
                      disabled
                      size="sm"
                      variant="outline"
                    >
                      <div
                        aria-hidden="true"
                        className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"
                      />
                      {tCommon('loading')}
                    </Button>
                  ) : (
                    <Button
                      className="mt-2 cursor-pointer"
                      disabled={isLoggingOut}
                      onClick={handleLogout}
                      size="sm"
                      variant="destructive"
                    >
                      <LogOut aria-hidden="true" className="mr-2 h-4 w-4" />
                      {isLoggingOut ? t('signingOut') : t('signOut')}
                    </Button>
                  )}
                </>
              )}
            </nav>
          </div>
        </>
      )}

      {/* Welcome Modal - Triggered by help icon */}
      <CensusWelcomeModal
        isOpen={isWelcomeOpen}
        onClose={() => setWelcomeOpen(false)}
        onDismiss={dismissModal}
      />
    </header>
  );
}
