'use client';

import { HelpCircle } from 'lucide-react';
import { useTranslations } from 'next-intl';
import type React from 'react';
import { useState } from 'react';
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
} from '@/components/ui/drawer';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { useIsMobile } from '@/hooks/use-mobile';

interface CommunityFeedbackHelpProps {
  className?: string;
  children?: React.ReactNode;
}

export function CommunityFeedbackHelp({
  className,
  children,
}: CommunityFeedbackHelpProps) {
  const isMobile = useIsMobile();
  const tForms = useTranslations('forms');
  const tCensus = useTranslations('census');
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  // Parse the placeholder text to get individual questions
  const placeholderText = tForms('enterCommunityFeedbackPlaceholder');
  const questions = placeholderText.split('\n').filter((q) => q.trim());

  // Mobile implementation with drawer
  if (isMobile) {
    return (
      <>
        <button
          aria-label="Show community feedback questions"
          className={`inline-flex cursor-pointer items-center justify-center transition-all duration-200 hover:text-primary focus:outline-none active:scale-95 ${className}`}
          onClick={() => setIsDrawerOpen(true)}
          type="button"
        >
          {children || (
            <HelpCircle className="community-feedback-icon-animated h-5 w-5 text-primary/70" />
          )}
        </button>

        <Drawer onOpenChange={setIsDrawerOpen} open={isDrawerOpen}>
          <DrawerContent className="max-h-[85vh] min-h-fit">
            <DrawerHeader>
              <DrawerTitle>{tCensus('communityFeedbackGuide')}</DrawerTitle>
              <DrawerDescription>
                {tCensus('considerSharingThoughts')}
              </DrawerDescription>
            </DrawerHeader>
            <div className="space-y-4 px-4 pb-8">
              {questions.map((question, index) => (
                <div
                  className="rounded-xl bg-card/50 p-4 transition-all duration-200 hover:scale-[1.02] hover:bg-accent/30"
                  key={index}
                >
                  <div className="flex items-start gap-3">
                    <div className="mt-2 h-2 w-2 flex-shrink-0 rounded-full bg-primary/60 transition-colors group-hover:bg-primary/80" />
                    <p className="pt-0.5 text-foreground text-sm leading-relaxed">
                      {question}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </DrawerContent>
        </Drawer>
      </>
    );
  }

  // Desktop implementation with tooltip
  return (
    <TooltipProvider>
      <Tooltip delayDuration={0}>
        <TooltipTrigger asChild>
          <button
            aria-label="Show community feedback questions"
            className={`inline-flex cursor-pointer items-center justify-center transition-all duration-300 hover:scale-110 hover:text-primary focus:outline-none ${className}`}
            type="button"
          >
            {children || (
              <HelpCircle className="community-feedback-icon-animated h-5 w-5 text-primary/70" />
            )}
          </button>
        </TooltipTrigger>
        <TooltipContent
          align="start"
          className="w-max max-w-[min(90vw,600px)] border bg-background/95 p-0 shadow-xl backdrop-blur-sm"
          hideArrow={true}
          side="bottom"
          sideOffset={12}
        >
          <div className="space-y-5 p-6">
            <div className="flex items-start gap-3 border-border/50 border-b pb-3">
              <HelpCircle className="mt-0.5 h-5 w-5 text-primary" />
              <div>
                <h3 className="font-semibold text-base text-foreground">
                  {tCensus('communityFeedbackGuide')}
                </h3>
                <p className="mt-0.5 text-muted-foreground text-sm">
                  {tCensus('considerSharingThoughts')}
                </p>
              </div>
            </div>

            <div className="space-y-3">
              {questions.map((question, index) => (
                <div
                  className="group rounded-lg bg-card/50 p-3 transition-all duration-200 hover:scale-[1.02] hover:bg-accent/30"
                  key={index}
                >
                  <div className="flex items-start gap-3">
                    <div className="mt-2 h-2 w-2 flex-shrink-0 rounded-full bg-primary/60 transition-colors group-hover:bg-primary/80" />
                    <p className="text-foreground text-sm leading-relaxed">
                      {question}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
