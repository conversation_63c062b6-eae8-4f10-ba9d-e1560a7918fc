'use client';

import { useState } from 'react';
import {
  type FieldError,
  type FieldErrorsImpl,
  type Merge,
  useFormContext,
} from 'react-hook-form';
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from '@/components/ui/input-otp';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';

interface OTPInputFieldProps {
  id: string;
  label?: string;
  required?: boolean;
  disabled?: boolean;
  error?:
    | FieldError
    | Merge<FieldError, FieldErrorsImpl<Record<string, unknown>>>;
  helperText?: string;
  length?: number;
  className?: string;
  value?: string;
  onChange?: (value: string) => void;
}

export function OTPInputField({
  id,
  label,
  required = false,
  disabled = false,
  error,
  helperText,
  length = 6,
  className,
  value: controlledValue,
  onChange: controlledOnChange,
}: OTPInputFieldProps) {
  // For uncontrolled usage (outside FormProvider)
  const [localValue, setLocalValue] = useState('');

  // Try to use form context, but don't fail if it's not available
  const formContext = useFormContext();

  // Determine if we're in a form context
  const inFormContext = !!formContext;

  // Get value and onChange handler based on context
  const value = inFormContext
    ? formContext.watch(id) || ''
    : controlledValue || localValue;

  const handleChange = (newValue: string) => {
    if (inFormContext) {
      formContext.setValue(id, newValue, { shouldValidate: true });
    } else if (controlledOnChange) {
      controlledOnChange(newValue);
    } else {
      setLocalValue(newValue);
    }
  };

  // Calculate the width of the OTP input group based on the length
  const containerWidth = length * 40 + (length / 2 - 1) * 8 + 20; // 40px per slot, 8px gap, 20px for separator

  return (
    <div className={cn('flex flex-col items-center space-y-4', className)}>
      <div className="flex flex-col" style={{ width: `${containerWidth}px` }}>
        {label && (
          <div className="mb-1">
            <Label className="font-medium text-sm" htmlFor={id}>
              {label}
              {required && <span className="ml-1 text-destructive">*</span>}
            </Label>
          </div>
        )}

        <div className="w-full">
          <InputOTP
            disabled={disabled}
            maxLength={length}
            onChange={handleChange}
            render={({ slots }) => (
              <InputOTPGroup>
                {slots.slice(0, length / 2).map((slot, index) => (
                  <InputOTPSlot
                    key={index}
                    {...slot}
                    className={cn(error && 'border-destructive')}
                  />
                ))}
                <div className="flex items-center justify-center">
                  <span className="font-medium text-muted-foreground">-</span>
                </div>
                {slots.slice(length / 2).map((slot, index) => (
                  <InputOTPSlot
                    key={index + length / 2}
                    {...slot}
                    className={cn(error && 'border-destructive')}
                  />
                ))}
              </InputOTPGroup>
            )}
            value={value}
          />
        </div>

        <div className="mt-1">
          {error && (
            <p className="text-destructive text-xs">
              {String(error.message || 'Validation error')}
            </p>
          )}

          {helperText && !error && (
            <p className="text-muted-foreground text-xs">{helperText}</p>
          )}
        </div>
      </div>
    </div>
  );
}
