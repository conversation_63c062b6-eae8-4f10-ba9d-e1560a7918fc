'use client';

import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { AdminSuburbAutocomplete } from '@/components/admin/suburb-autocomplete';
import { Button } from '@/components/ui/button';
import { DatePicker } from '@/components/ui/date-picker';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
} from '@/components/ui/drawer';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useIsMobile } from '@/hooks/use-mobile';
import { useMessage } from '@/hooks/useMessage';
import type { IHouseholdWithDetails } from '@/lib/db/households';
import { formatDateForDatabase } from '@/lib/utils/date-time';
import { zodResolver } from '@/lib/utils/zod-resolver-compat';
import {
  type ClientAdminHouseholdEditFormValues,
  createClientAdminHouseholdEditSchema,
} from '@/lib/validation/client/admin-client';
import type { ICensusYear } from '@/types';

// Use the database interface directly for type consistency
type HouseholdWithDetails = IHouseholdWithDetails;

// Interface for API request data
interface UpdateHouseholdApiData {
  suburb: string;
  head_first_name: string;
  head_last_name: string;
  head_date_of_birth: string;
  head_mobile_phone: string;
  head_gender: 'male' | 'female' | 'other';
  head_hobby?: string;
  head_occupation?: string;
  household_comment?: string;
}

interface EditHouseholdDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  household: HouseholdWithDetails;
  censusYears: ICensusYear[];
  onHouseholdUpdated: () => void;
}

export function EditHouseholdDialog({
  open,
  onOpenChange,
  household,
  onHouseholdUpdated,
}: EditHouseholdDialogProps) {
  const { showError } = useMessage();
  const isMobile = useIsMobile();
  const _t = useTranslations('admin');
  const tCommon = useTranslations('common');
  const tForms = useTranslations('forms');
  const tCensus = useTranslations('census');
  const tValidation = useTranslations('validation');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [members, setMembers] = useState<any[]>([]);
  const [communityFeedback, setCommunityFeedback] = useState<string>('');
  const [loading, setLoading] = useState(true);

  // Create client-side validation schema with translations
  const householdSchema = createClientAdminHouseholdEditSchema(tValidation);

  // Find head member to get hobby and occupation data
  const headMember = members?.find((member) => member.relationship === 'head');

  // Initialize form with household data (mapping API fields to form fields)
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch,
    control,
  } = useForm<ClientAdminHouseholdEditFormValues>({
    resolver: zodResolver(householdSchema),
    defaultValues: {
      suburb: household.suburb,
      headFirstName: household.headName?.split(' ')[0] || '',
      headLastName: household.headName?.split(' ').slice(1).join(' ') || '',
      headDateOfBirth: household.headDateOfBirth
        ? new Date(household.headDateOfBirth)
        : undefined,
      mobilePhone: household.headContact || '',
      gender: household.headGender || 'male', // Use the headGender from the API or default to 'male'
      headHobby: headMember?.hobby || '',
      headOccupation: headMember?.occupation || '',
      householdComment: communityFeedback || '',
    },
  });

  // Watch the date of birth field for display
  const watchedDateOfBirth = watch('headDateOfBirth');

  // Fetch household details when dialog opens
  useEffect(() => {
    if (open && household.id) {
      const fetchHouseholdDetails = async () => {
        try {
          setLoading(true);
          const response = await fetch(`/api/admin/households/${household.id}`);
          if (!response.ok) {
            throw new Error('Failed to fetch household details');
          }
          const data = await response.json();
          setMembers(data.members || []);
          setCommunityFeedback(data.communityFeedback || '');
        } catch (error) {
          console.error('Error fetching household details:', error);
          showError('failedToLoadHouseholdDetails');
        } finally {
          setLoading(false);
        }
      };

      fetchHouseholdDetails();
    }
  }, [open, household.id, showError]);

  // Update form values when household changes
  useEffect(() => {
    if (!loading) {
      const headMember = members?.find(
        (member) => member.relationship === 'head'
      );
      reset({
        suburb: household.suburb,
        headFirstName: household.headName?.split(' ')[0] || '',
        headLastName: household.headName?.split(' ').slice(1).join(' ') || '',
        headDateOfBirth: household.headDateOfBirth
          ? new Date(household.headDateOfBirth)
          : undefined,
        mobilePhone: household.headContact || '',
        gender: household.headGender || 'male', // Use the headGender from the API or default to 'male'
        headHobby: headMember?.hobby || '',
        headOccupation: headMember?.occupation || '',
        householdComment: communityFeedback || '',
      });
    }
  }, [household, members, communityFeedback, reset, loading]);

  // Handle dialog close
  const handleDialogClose = (open: boolean) => {
    if (!(isSubmitting && open)) {
      reset();

      // Force body to be interactive (admin portal pointer-events fix)
      if (!open) {
        document.body.style.pointerEvents = 'auto';
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';
      }

      onOpenChange(open);
    }
  };

  // Handle date selection - match census form pattern exactly
  const handleDateSelect = (date: Date | null) => {
    setValue('headDateOfBirth', date as any);
  };

  // Ensure body is interactive when dialog is closed (admin portal pointer-events fix)
  useEffect(() => {
    if (!open) {
      // Force pointer-events to be enabled
      document.body.style.pointerEvents = 'auto';
      // Remove any overflow hidden that might have been added
      document.body.style.overflow = '';
      // Remove any padding right that might have been added to compensate for scrollbar
      document.body.style.paddingRight = '';
    }

    // Cleanup function to ensure proper cleanup when component unmounts
    return () => {
      // Force pointer-events to be enabled
      document.body.style.pointerEvents = 'auto';
      // Remove any overflow hidden that might have been added
      document.body.style.overflow = '';
      // Remove any padding right that might have been added to compensate for scrollbar
      document.body.style.paddingRight = '';
    };
  }, [open]);

  // Handle form submission
  const onSubmit = async (data: ClientAdminHouseholdEditFormValues) => {
    try {
      setIsSubmitting(true);

      // Transform data to match API expectations (map form fields to API fields)
      const apiData: UpdateHouseholdApiData = {
        suburb: data.suburb,
        head_first_name: data.headFirstName,
        head_last_name: data.headLastName,
        head_date_of_birth: data.headDateOfBirth
          ? formatDateForDatabase(data.headDateOfBirth)
          : '',
        head_mobile_phone: data.mobilePhone,
        head_gender: data.gender,
        head_hobby: data.headHobby || '',
        head_occupation: data.headOccupation || '',
        household_comment: data.householdComment || '',
      };

      // Submit form data to API
      const response = await fetch(`/api/admin/households/${household.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(apiData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || tCommon('failedToUpdateHousehold'));
      }

      // Reset form and close dialog
      onHouseholdUpdated();
    } catch (error) {
      console.error('Error updating household:', error);
      showError('failedToUpdateHousehold');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Shared form content component for both mobile and desktop
  const FormContent = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <div className="mx-auto mb-2 h-8 w-8 animate-spin rounded-full border-primary border-b-2" />
            <p className="text-muted-foreground text-sm">
              {tCommon('loading')}
            </p>
          </div>
        </div>
      );
    }

    return (
      <div className="grid gap-4 py-4">
        {/* Household Information */}
        <div className="mb-2">
          <h3 className="font-medium text-sm">
            {tCommon('householdInformation')}
          </h3>
        </div>

        <AdminSuburbAutocomplete
          control={control}
          error={errors.suburb}
          label={tCommon('suburb')}
          name="suburb"
          placeholder={tForms('searchForSuburb')}
          required
        />

        {/* Household Head Information */}
        <div className="mt-4 mb-2">
          <h3 className="font-medium text-sm">
            {tCommon('householdHeadInformation')}
          </h3>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="grid gap-2">
            <Label
              className={errors.headFirstName ? 'text-destructive' : ''}
              htmlFor="headFirstName"
            >
              {tForms('firstName')} <span className="text-destructive">*</span>
            </Label>
            <Input
              id="headFirstName"
              {...register('headFirstName')}
              className={errors.headFirstName ? 'border-destructive' : ''}
              placeholder={tForms('enterFirstName')}
            />
            {errors.headFirstName && (
              <p className="text-destructive text-xs">
                {errors.headFirstName.message}
              </p>
            )}
          </div>

          <div className="grid gap-2">
            <Label
              className={errors.headLastName ? 'text-destructive' : ''}
              htmlFor="headLastName"
            >
              {tForms('lastName')} <span className="text-destructive">*</span>
            </Label>
            <Input
              id="headLastName"
              {...register('headLastName')}
              className={errors.headLastName ? 'border-destructive' : ''}
              placeholder={tForms('enterLastName')}
            />
            {errors.headLastName && (
              <p className="text-destructive text-xs">
                {errors.headLastName.message}
              </p>
            )}
          </div>
        </div>

        {/* Date of Birth + Gender Row */}
        <div className="grid grid-cols-2 gap-4">
          <div className="grid gap-2">
            <Label className={errors.headDateOfBirth ? 'text-destructive' : ''}>
              {tForms('householdHeadDateOfBirth')}{' '}
              <span className="text-destructive">*</span>
            </Label>
            <DatePicker
              className={errors.headDateOfBirth ? 'border-destructive' : ''}
              date={watchedDateOfBirth ? new Date(watchedDateOfBirth) : null}
              placeholderText={tForms('selectDateOfBirthPlaceholder')}
              preventFutureDates={true}
              setDate={handleDateSelect}
            />
            {errors.headDateOfBirth && (
              <p className="text-destructive text-xs">
                {errors.headDateOfBirth.message}
              </p>
            )}
          </div>

          <div className="grid gap-2">
            <Label
              className={errors.gender ? 'text-destructive' : ''}
              htmlFor="gender"
            >
              {tForms('gender')} <span className="text-destructive">*</span>
            </Label>
            <Select
              onValueChange={(value) =>
                setValue('gender', value as 'male' | 'female' | 'other')
              }
              value={watch('gender')}
            >
              <SelectTrigger
                className={errors.gender ? 'border-destructive' : ''}
                id="gender"
              >
                <SelectValue placeholder={tForms('selectGender')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="male">{tForms('male')}</SelectItem>
                <SelectItem value="female">{tForms('female')}</SelectItem>
                <SelectItem value="other">{tForms('other')}</SelectItem>
              </SelectContent>
            </Select>
            {errors.gender && (
              <p className="text-destructive text-xs">
                {errors.gender.message}
              </p>
            )}
          </div>
        </div>

        {/* Mobile Phone Row */}
        <div className="grid gap-2">
          <Label
            className={errors.mobilePhone ? 'text-destructive' : ''}
            htmlFor="mobilePhone"
          >
            {tForms('mobilePhone')} <span className="text-destructive">*</span>
          </Label>
          <Input
            id="mobilePhone"
            {...register('mobilePhone')}
            className={errors.mobilePhone ? 'border-destructive' : ''}
            placeholder={tForms('enter10digitMobileNumber')}
          />
          {errors.mobilePhone && (
            <p className="text-destructive text-xs">
              {errors.mobilePhone.message}
            </p>
          )}
        </div>

        {/* Hobby + Occupation Row */}
        <div className="grid grid-cols-2 gap-4">
          <div className="grid gap-2">
            <Label
              className={errors.headHobby ? 'text-destructive' : ''}
              htmlFor="headHobby"
            >
              {tForms('hobby')}
            </Label>
            <Input
              id="headHobby"
              {...register('headHobby')}
              className={errors.headHobby ? 'border-destructive' : ''}
              placeholder={tForms('enterHobbyOrInterests')}
            />
            {errors.headHobby && (
              <p className="text-destructive text-xs">
                {errors.headHobby.message}
              </p>
            )}
          </div>

          <div className="grid gap-2">
            <Label
              className={errors.headOccupation ? 'text-destructive' : ''}
              htmlFor="headOccupation"
            >
              {tForms('occupation')}
            </Label>
            <Input
              id="headOccupation"
              {...register('headOccupation')}
              className={errors.headOccupation ? 'border-destructive' : ''}
              placeholder={tForms('enterOccupationPlaceholder')}
            />
            {errors.headOccupation && (
              <p className="text-destructive text-xs">
                {errors.headOccupation.message}
              </p>
            )}
          </div>
        </div>

        {/* Community Feedback Section */}
        <div className="grid gap-2">
          <Label
            className={errors.householdComment ? 'text-destructive' : ''}
            htmlFor="householdComment"
          >
            {tCensus('communityFeedback')}
          </Label>
          <Textarea
            id="householdComment"
            {...register('householdComment')}
            className={errors.householdComment ? 'border-destructive' : ''}
            rows={4}
          />
          {errors.householdComment && (
            <p className="text-destructive text-xs">
              {errors.householdComment.message}
            </p>
          )}
        </div>
      </div>
    );
  };

  // Action buttons component for both mobile and desktop
  const ActionButtons = () => (
    <>
      <Button
        disabled={isSubmitting}
        onClick={() => handleDialogClose(false)}
        type="button"
        variant="outline"
      >
        {tCommon('cancel')}
      </Button>
      <Button disabled={isSubmitting} type="submit" variant="default">
        {isSubmitting ? tCommon('saving') : tCommon('save')}
      </Button>
    </>
  );

  // Mobile implementation using Drawer (native swipe-to-dismiss from bottom)
  // Features: Visual handle bar, smooth swipe gestures, and dynamic height for better content visibility
  if (isMobile) {
    return (
      <Drawer onOpenChange={handleDialogClose} open={open}>
        <DrawerContent className="flex max-h-[80vh] min-h-[60vh] flex-col">
          <DrawerHeader>
            <DrawerTitle>{tCommon('editHousehold')}</DrawerTitle>
            <DrawerDescription>
              {tCommon('updateHouseholdInformationForId', {
                id: household.id.toString(),
              })}
            </DrawerDescription>
          </DrawerHeader>
          <div className="scrollbar-hide flex-1 overflow-y-auto px-4 pb-4">
            <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
              <FormContent />

              <div className="mt-6 pt-4">
                <div className="flex justify-end gap-2">
                  <ActionButtons />
                </div>
              </div>
            </form>
          </div>
        </DrawerContent>
      </Drawer>
    );
  }

  // Desktop implementation using Dialog (unchanged)
  return (
    <Dialog onOpenChange={handleDialogClose} open={open}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{tCommon('editHousehold')}</DialogTitle>
          <DialogDescription>
            {tCommon('updateHouseholdInformationForId', {
              id: household.id.toString(),
            })}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)}>
          <FormContent />

          <DialogFooter>
            <ActionButtons />
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
