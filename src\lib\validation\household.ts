import { getTranslations } from 'next-intl/server';
import { z } from 'zod/v4';

/**
 * Validation schemas for household management in the admin portal
 * These schemas now support translations using next-intl's errorMap pattern
 */

/**
 * Create household schema with translations (for household registration)
 */
export async function createHouseholdSchemaWithTranslations(
  locale: 'en' | 'zh-CN' = 'en'
) {
  const t = await getTranslations({ locale, namespace: 'validation' });

  return z.object({
    suburb: z.string().min(1, { error: t('suburbRequired') }),
    head_first_name: z
      .string()
      .transform((val) => val.trim())
      .pipe(z.string().min(1, { error: t('firstNameRequired') })),
    head_last_name: z
      .string()
      .transform((val) => val.trim())
      .pipe(z.string().min(1, { error: t('lastNameRequired') })),
    head_mobile_phone: z
      .string()
      .min(10, { error: t('mobilePhoneMustBeAtLeast10Digi') })
      .max(10, { error: t('mobilePhoneCannotExceed10Digit') })
      .regex(/^04\d{8}$/, { error: t('mobilePhoneCanOnlyContainNumbe') }),
    head_gender: z.enum(['male', 'female', 'other'], {
      error: t('genderRequired'),
    }),
  });
}

/**
 * Create admin household schema with translations (for admin household creation)
 */
export async function createAdminHouseholdSchema(
  locale: 'en' | 'zh-CN' = 'en'
) {
  const t = await getTranslations({ locale, namespace: 'validation' });

  return z.object({
    suburb: z.string().min(1, { error: t('suburbRequired') }),
    firstCensusYearId: z.number({
      error: t('firstCensusYearRequired'),
    }),
    lastCensusYearId: z.number({
      error: t('lastCensusYearRequired'),
    }),
  });
}

/**
 * Create update household schema with translations (for admin household updates)
 */
export async function createUpdateHouseholdSchema(
  locale: 'en' | 'zh-CN' = 'en'
) {
  const t = await getTranslations({ locale, namespace: 'validation' });

  return z.object({
    suburb: z.string().min(1, { error: t('suburbRequired') }),
    head_first_name: z
      .string()
      .transform((val) => val.trim())
      .pipe(z.string().min(1, { error: t('firstNameRequired') })),
    head_last_name: z
      .string()
      .transform((val) => val.trim())
      .pipe(z.string().min(1, { error: t('lastNameRequired') })),
    head_date_of_birth: z.string().min(1, { error: t('dateOfBirthRequired') }),
    head_mobile_phone: z
      .string()
      .min(10, { error: t('mobilePhoneMustBeAtLeast10Digi') })
      .max(10, { error: t('mobilePhoneCannotExceed10Digit') })
      .regex(/^04\d{8}$/, { error: t('mobilePhoneCanOnlyContainNumbe') }),
    head_gender: z.enum(['male', 'female', 'other'], {
      error: t('genderRequired'),
    }),
    head_hobby: z
      .string()
      .transform((val) => val.trim())
      .pipe(z.string().max(100, { error: t('hobbyTooLong') }))
      .optional()
      .or(z.literal('')),
    head_occupation: z
      .string()
      .transform((val) => val.trim())
      .pipe(z.string().max(100, { error: t('occupationTooLong') }))
      .optional()
      .or(z.literal('')),
    household_comment: z
      .string()
      .transform((val) => val.trim())
      .pipe(z.string().max(1000, { error: t('householdCommentTooLong') }))
      .optional()
      .or(z.literal('')),
  });
}

/**
 * Create bulk household validation schema with translations
 */
export async function createBulkHouseholdValidationSchema(
  locale: 'en' | 'zh-CN' = 'en'
) {
  const t = await getTranslations({ locale, namespace: 'validation' });

  return z.object({
    householdIds: z
      .array(z.number())
      .min(1, { error: t('atLeastOneHouseholdIdRequired') }),
  });
}

// Type exports for server-side validation
export type ServerCreateHouseholdFormValues = z.infer<
  Awaited<ReturnType<typeof createHouseholdSchemaWithTranslations>>
>;
export type ServerUpdateHouseholdFormValues = ServerCreateHouseholdFormValues;

/**
 * Create household filter schema with translations
 */
export async function createHouseholdFilterSchema(
  locale: 'en' | 'zh-CN' = 'en'
) {
  const t = await getTranslations({ locale, namespace: 'validation' });

  return z.object({
    searchTerm: z.string().optional(),
    censusYearId: z.number().optional().nullable(),
    page: z
      .number()
      .min(1, { error: t('pageMustBeAtLeast1') })
      .default(1),
    pageSize: z
      .number()
      .min(1, { error: t('pageSizeMustBeAtLeast1') })
      .max(100, { error: t('pageSizeCannotExceed100') })
      .default(20),
    sortBy: z
      .enum([
        'id',
        'suburb',
        'first_census_year',
        'last_census_year',
        'member_count',
        'createdAt',
        'updatedAt',
      ])
      .default('id'),
    sortOrder: z.enum(['asc', 'desc']).default('asc'),
  });
}

// Type definitions for form values
export type HouseholdFilterValues = z.infer<
  Awaited<ReturnType<typeof createHouseholdFilterSchema>>
>;
