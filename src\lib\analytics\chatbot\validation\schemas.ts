/**
 * Validation Schemas for Analytics Chatbot
 *
 * This file contains all Zod validation schemas for the analytics chatbot system,
 * including request validation and intent detection schemas.
 */

import { z } from 'zod/v4';

// --- Zod <PERSON>hemas ---

// 1. Validation for the incoming request to this API route (AI SDK format)
export const chatbotRequestSchema = z.object({
  messages: z
    .array(
      z.object({
        role: z.enum(['user', 'assistant', 'system']),
        content: z.string(),
        id: z.string().optional(),
        createdAt: z.union([z.string(), z.date()]).optional(),
      })
    )
    .min(1, 'At least one message is required'),
});

// --- Intent-Based Query System Schemas ---

// Zod schema for intent validation
export const queryIntentSchema = z.object({
  dataType: z.enum([
    'member_demographics',
    'household_info',
    'sacrament_records',
    'census_participation',
    'temporal_analysis',
    'general',
  ]),
  analysisType: z.enum([
    'count',
    'distribution',
    'list',
    'chart',
    'overview',
    'specific',
  ]),
  filters: z
    .object({
      gender: z.string().nullable().optional(),
      ageRange: z
        .object({
          min: z.number().optional(),
          max: z.number().optional(),
        })
        .nullable()
        .optional(),
      location: z.string().nullable().optional(),
      sacramentType: z.string().nullable().optional(),
      censusYear: z.number().nullable().optional(),
      relationship: z.string().nullable().optional(),
      hobby: z.string().nullable().optional(),
      occupation: z.string().nullable().optional(),
    })
    .optional(),
  chartRequested: z.boolean().optional(),
  confidence: z.number().min(0).max(1),
});

// Type inference from Zod schemas
export type ChatbotRequestInput = z.infer<typeof chatbotRequestSchema>;
export type QueryIntentInput = z.infer<typeof queryIntentSchema>;
