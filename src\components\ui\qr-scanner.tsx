'use client';

import { <PERSON>ert<PERSON><PERSON>cle, Camera, CameraOff, ScanQrCode } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useCallback, useEffect, useRef, useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
} from '@/components/ui/drawer';
import { useIsMobile } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';

interface QrScannerModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onQrCodeScanned: (code: string) => void;
}

// QR Scanner instance type (using any for compatibility with library)
type QrScannerInstance = any;

type ScannerState =
  | 'idle'
  | 'requesting-permission'
  | 'scanning'
  | 'success'
  | 'error'
  | 'no-camera';

export function QrScannerModal({
  open,
  onOpenChange,
  onQrCodeScanned,
}: QrScannerModalProps) {
  const isMobile = useIsMobile();
  const t = useTranslations('qrScanner');
  const tCommon = useTranslations('common');

  const [scannerState, setScannerState] = useState<ScannerState>('idle');
  const [errorMessage, setErrorMessage] = useState<string>('');
  const videoRef = useRef<HTMLVideoElement>(null);
  const qrScannerRef = useRef<QrScannerInstance | null>(null);

  // Cleanup scanner function
  const cleanupScanner = useCallback(() => {
    if (qrScannerRef.current) {
      try {
        qrScannerRef.current.stop();
        qrScannerRef.current.destroy();
        qrScannerRef.current = null;
      } catch (_error) {
        // Silently handle cleanup errors
      }
    }
  }, []);

  // Initialize scanner function
  const initializeScanner = useCallback(async () => {
    // Prevent multiple initializations
    if (qrScannerRef.current) {
      return;
    }

    try {
      // Check if we're on HTTPS or localhost or local network
      const isSecureContext =
        window.location.protocol === 'https:' ||
        window.location.hostname === 'localhost' ||
        window.location.hostname === '127.0.0.1' ||
        window.location.hostname.startsWith('192.168.') ||
        window.location.hostname.startsWith('10.') ||
        window.location.hostname.endsWith('.local');

      if (!isSecureContext) {
        throw new Error(t('httpsRequired'));
      }

      // Check if getUserMedia is available
      if (!navigator.mediaDevices?.getUserMedia) {
        throw new Error(t('cameraNotSupported'));
      }

      // Request camera permission and get video stream
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: 'environment', // Prefer back camera
          width: { ideal: 1280 },
          height: { ideal: 720 },
        },
      });

      if (!videoRef.current) {
        throw new Error('Video element not found');
      }

      // Set video stream
      videoRef.current.srcObject = stream;
      await videoRef.current.play();

      // Import QrScanner dynamically
      const QrScanner = (await import('qr-scanner')).default;

      // Initialize QR scanner
      const scanner = new QrScanner(
        videoRef.current,
        (result) => {
          if (process.env.NODE_ENV === 'development') {
            console.log('✅ QR Code detected:', result.data);
          }
          onQrCodeScanned(result.data);
          cleanupScanner();
          onOpenChange(false);
        },
        {
          highlightScanRegion: true,
          highlightCodeOutline: true,
          preferredCamera: 'environment',
          maxScansPerSecond: 5,
        }
      );

      qrScannerRef.current = scanner;
      await scanner.start();
      setScannerState('scanning');

      if (process.env.NODE_ENV === 'development') {
        console.log('✅ QR Scanner initialized successfully');
      }
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('❌ QR Scanner initialization failed:', error);
      }

      const errorObj = error as Error;
      let errorMessage = t('scannerError');

      if (errorObj.name === 'NotAllowedError') {
        errorMessage = t('cameraPermissionDenied');
      } else if (errorObj.name === 'NotFoundError') {
        errorMessage = t('noCameraFound');
      } else if (errorObj.name === 'NotSupportedError') {
        errorMessage = t('cameraNotSupported');
      } else if (errorObj.name === 'NotReadableError') {
        errorMessage = t('cameraInUse');
      } else if (errorObj.message) {
        errorMessage = errorObj.message;
      }

      setErrorMessage(errorMessage);
      setScannerState('error');
    }
  }, [t, onQrCodeScanned, cleanupScanner, onOpenChange]);

  // Initialize QR scanner when modal opens
  useEffect(() => {
    if (open) {
      setScannerState('requesting-permission');
      setErrorMessage('');
      // Start initialization process
      initializeScanner();
    } else {
      cleanupScanner();
      setScannerState('idle');
    }
  }, [
    open,
    cleanupScanner, // Start initialization process
    initializeScanner,
  ]); // eslint-disable-line react-hooks/exhaustive-deps

  // Initialize scanner when video element becomes available
  useEffect(() => {
    if (
      scannerState === 'scanning' &&
      videoRef.current &&
      !qrScannerRef.current
    ) {
      if (process.env.NODE_ENV === 'development') {
        console.log('✅ Video element ready, initializing scanner...');
      }
      // Add small delay to prevent race conditions
      const timer = setTimeout(() => {
        initializeScanner();
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [scannerState, initializeScanner]); // eslint-disable-line react-hooks/exhaustive-deps

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cleanupScanner();
    };
  }, [cleanupScanner]);

  const handleScanSuccess = (result: string) => {
    // Accept QR codes if we have a result and scanner is active (not in error state)
    if (result && scannerState !== 'error' && scannerState !== 'no-camera') {
      // Immediately fill the input and close modal - no success screen needed
      onQrCodeScanned(result);
      onOpenChange(false);
    }
  };

  const handleScanError = (error: unknown) => {
    const errorObj = error as Error;

    // Handle specific camera errors
    if (
      errorObj.name === 'NotAllowedError' ||
      errorObj.message?.includes('permission')
    ) {
      setErrorMessage(t('cameraPermissionDenied'));
      setScannerState('error');
    } else if (
      errorObj.name === 'NotFoundError' ||
      errorObj.message?.includes('camera') ||
      errorObj.message?.includes('device')
    ) {
      setErrorMessage('No camera device found on this device');
      setScannerState('no-camera');
    } else if (
      errorObj.name === 'NotReadableError' ||
      errorObj.message?.includes('use')
    ) {
      setErrorMessage('Camera is currently being used by another application');
      setScannerState('error');
    } else if (errorObj.name === 'OverconstrainedError') {
      setErrorMessage('Camera settings are not supported by your device');
      setScannerState('error');
    } else if (
      errorObj.name === 'AbortError' ||
      errorObj.message?.includes('interrupted')
    ) {
      // Don't show error for AbortError during development
      return;
    } else if (errorObj.message?.includes('https')) {
      setErrorMessage('Camera access requires a secure connection (HTTPS)');
      setScannerState('error');
    } else {
      setErrorMessage(errorObj.message || t('scannerError'));
      setScannerState('error');
    }
  };

  const handleTryAgain = () => {
    setScannerState('requesting-permission');
    setErrorMessage('');

    // Add a small delay to let the user see the state change
    setTimeout(() => {
      initializeScanner();
    }, 500);
  };

  const renderScannerContent = () => {
    switch (scannerState) {
      case 'requesting-permission':
        return (
          <div className="flex h-64 flex-col items-center justify-center space-y-4">
            <Camera className="h-12 w-12 animate-pulse text-primary" />
            <div className="space-y-3 text-center">
              <p className="font-medium text-lg">
                {t('requestingCameraAccess')}
              </p>
              <p className="text-muted-foreground text-sm">
                {t('pleaseAllowCameraAccess')}
              </p>
              <div className="flex items-center justify-center space-x-1 text-muted-foreground text-xs">
                <div
                  className="h-1 w-1 animate-bounce rounded-full bg-current"
                  style={{ animationDelay: '0ms' }}
                />
                <div
                  className="h-1 w-1 animate-bounce rounded-full bg-current"
                  style={{ animationDelay: '150ms' }}
                />
                <div
                  className="h-1 w-1 animate-bounce rounded-full bg-current"
                  style={{ animationDelay: '300ms' }}
                />
              </div>
            </div>
          </div>
        );

      case 'scanning':
        return (
          <div className="relative">
            <video
              className={cn(
                'w-full rounded-lg bg-black',
                isMobile ? 'h-[300px]' : 'h-[400px]'
              )}
              muted
              playsInline
              ref={videoRef}
              style={{ objectFit: 'cover' }}
            />

            <div className="-translate-x-1/2 absolute bottom-4 left-1/2 transform rounded-full bg-black/70 px-3 py-1 text-sm text-white">
              {t('scanQrCode')}
            </div>
          </div>
        );

      // Success state removed - we immediately close modal and fill input

      case 'error':
      case 'no-camera':
        return (
          <div className="flex h-64 flex-col items-center justify-center space-y-4">
            {scannerState === 'no-camera' ? (
              <CameraOff className="h-12 w-12 text-muted-foreground" />
            ) : (
              <AlertCircle className="h-12 w-12 text-destructive" />
            )}
            <div className="space-y-3 text-center">
              <p className="font-medium text-destructive">
                {t('scannerError')}
              </p>
              {errorMessage && errorMessage !== t('scannerError') && (
                <p className="text-muted-foreground text-sm">{errorMessage}</p>
              )}

              {/* Permission denied specific help */}
              {errorMessage.includes('permission') && (
                <div className="rounded-lg bg-muted/50 p-3 text-muted-foreground text-xs">
                  <p className="mb-1 font-medium">🔧 {t('permissionHelp')}:</p>
                  <p>• {t('clickLockIconInAddressBar')}</p>
                  <p>• {t('selectAllowForCamera')}</p>
                  <p>• {t('refreshPageAndTryAgain')}</p>
                  <div className="mt-2 rounded bg-blue-50 p-2 text-blue-700 dark:bg-blue-950 dark:text-blue-300">
                    <p className="font-medium text-xs">
                      💡 If you previously blocked camera access, the browser
                      won&apos;t ask again. Please follow the steps above to
                      manually allow camera access.
                    </p>
                  </div>
                </div>
              )}
            </div>
            <div className="flex gap-2">
              <Button onClick={() => onOpenChange(false)} variant="outline">
                {tCommon('cancel')}
              </Button>
              <Button onClick={handleTryAgain}>{t('tryAgain')}</Button>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  const content = <div className="space-y-4">{renderScannerContent()}</div>;

  // Mobile implementation using Drawer (native swipe-to-dismiss from bottom)
  if (isMobile) {
    return (
      <Drawer onOpenChange={onOpenChange} open={open}>
        <DrawerContent className="flex max-h-[80vh] min-h-[60vh] flex-col">
          <DrawerHeader>
            <DrawerTitle>{t('scanQrCode')}</DrawerTitle>
            <DrawerDescription>{t('pointCameraAtQrCode')}</DrawerDescription>
          </DrawerHeader>
          <div className="scrollbar-hide flex-1 overflow-y-auto px-4">
            {content}
          </div>
        </DrawerContent>
      </Drawer>
    );
  }

  // Desktop implementation using Dialog
  return (
    <Dialog onOpenChange={onOpenChange} open={open}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <ScanQrCode className="h-4 w-4" />
            {t('scanQrCode')}
          </DialogTitle>
          <DialogDescription>{t('pointCameraAtQrCode')}</DialogDescription>
        </DialogHeader>

        {content}
      </DialogContent>
    </Dialog>
  );
}
