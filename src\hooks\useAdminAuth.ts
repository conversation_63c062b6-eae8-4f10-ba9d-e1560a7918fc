'use client';

import { useRouter } from 'next/navigation';
import { signIn, signOut, useSession } from 'next-auth/react';
import { useAlert } from '@/contexts/AlertContext';
import { useAuthSystem } from '@/providers/combined-auth-provider';

/**
 * Custom hook for admin authentication
 * This hook ensures that admin authentication is completely separate from census authentication
 * by explicitly setting the basePath and signout URL
 */
export function useAdminAuth() {
  // Use the admin auth session with the correct basePath
  const { data: session, status } = useSession({
    required: false,
  });

  const router = useRouter();
  const { showAlert } = useAlert();
  const { setActiveSystem } = useAuthSystem();

  // NOTE: Removed setActiveSystem call - now handled by pathname-based logic in CombinedAuthProvider
  // This prevents race conditions and competing state updates

  const isAuthenticated =
    status === 'authenticated' && session?.user?.role === 'admin';
  const isLoading = status === 'loading';

  /**
   * Sign in with admin credentials
   */
  const signInWithCredentials = async (username: string, password: string) => {
    try {
      const result = await signIn('credentials', {
        username,
        password,
        redirect: false,
        // Ensure we're using the admin auth endpoint
        signinUrl: '/api/auth/signin',
        callbackUrl: '/admin/dashboard',
      });

      if (result?.error) {
        // Handle specific errors
        if (result.error === 'TotpRequired') {
          return { success: false, requireTotp: true };
        }

        return { success: false, error: result.error };
      }

      if (result?.url) {
        // Show success message
        showAlert('success', 'Login successful');

        // Redirect to the dashboard
        router.push(result.url);
        return { success: true };
      }

      return { success: false, error: 'Unknown error' };
    } catch (error) {
      console.error('Admin authentication error:', error);
      showAlert('error', 'An error occurred during authentication');
      return { success: false, error: 'Authentication error' };
    }
  };

  /**
   * Sign out from admin session
   */
  const signOutFromAdmin = async () => {
    await signOut({
      callbackUrl: '/admin/login',
    });
  };

  return {
    session,
    status,
    isAuthenticated,
    isLoading,
    signInWithCredentials,
    signOutFromAdmin,
  };
}
