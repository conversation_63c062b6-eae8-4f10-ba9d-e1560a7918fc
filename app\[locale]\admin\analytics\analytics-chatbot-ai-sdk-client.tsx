'use client';

import { useChat } from '@ai-sdk/react';
import {
  AlertCircle,
  ArrowDown,
  ArrowUp,
  Check,
  CheckSquare,
  Copy,
  HelpCircle,
  Home,
  MessageSquare,
  RefreshCw,
  Scroll,
  Trash2,
  Users,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { ChatbotHelpDialog } from '@/components/admin/analytics/chatbot-help-dialog';
import { FluidIcon } from '@/components/admin/fluid-icon';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useMessage } from '@/hooks/useMessage';
import RichMessageRenderer from '../../../../components/admin/analytics/rich-message-renderer';

// import { ChatErrorBoundary } from '@/components/admin/analytics/chat-error-boundary';

// Type definitions for component props

// No props needed for current implementation
type AnalyticsChatbotAISDKClientProps = Record<string, never>;

function AnalyticsChatbotAISDKClientInner() {
  const { showSuccess, showError } = useMessage();
  const t = useTranslations('admin');
  const tCommon = useTranslations('common');

  // Conversation persistence keys
  const STORAGE_KEY = 'wsccc-analytics-chat-history';
  const CHAT_STATE_KEY = 'wsccc-analytics-chat-state';

  // PROFESSIONAL FIX: Memoize initial messages to prevent re-mounting
  const initialMessages = useMemo(() => {
    if (typeof window === 'undefined') {
      return [];
    }
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        // Validate and convert stored messages to proper format
        return parsed.map(
          (msg: {
            id: string;
            role: string;
            content: string;
            createdAt?: string;
          }) => ({
            ...msg,
            createdAt: msg.createdAt ? new Date(msg.createdAt) : new Date(),
          })
        );
      }
    } catch (_error) {
      if (process.env.NODE_ENV === 'development') {
      }
    }
    return [];
  }, []);

  // PROFESSIONAL FIX: Memoize initial chat state to prevent re-mounting
  const initialChatState = useMemo(() => {
    if (typeof window === 'undefined') {
      return false;
    }
    try {
      const stored = localStorage.getItem(CHAT_STATE_KEY);
      if (stored) {
        return JSON.parse(stored);
      }
      // If no stored state, check if there are existing messages
      return initialMessages.length > 0;
    } catch (_error) {
      if (process.env.NODE_ENV === 'development') {
      }
      // Fallback: check if there are existing messages
      return initialMessages.length > 0;
    }
  }, [initialMessages]);

  // AI SDK useChat hook - replaces manual message management
  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    isLoading: isAILoading,
    error,
    setMessages,
    setInput,
    reload,
  } = useChat({
    api: '/api/admin/analytics/chatbot-ai-sdk',
    id: 'admin-analytics-chat',
    initialMessages, // Use memoized value
    onError: (error) => {
      // Enhanced error handling to prevent duplicate error displays
      const isValidationError =
        error.message &&
        (error.message.includes('请求格式无效') ||
          error.message.includes('Invalid request format') ||
          error.message.includes('400'));

      // Only log to console in development and for non-validation errors
      // Validation errors are better shown in the UI error box
      if (process.env.NODE_ENV === 'development' && !isValidationError) {
      }

      // INDUSTRY BEST PRACTICE: Check for security violations and apply selective filtering
      if (error.message?.includes('400')) {
        // Security violation detected - remove only the malicious query, preserve legitimate context
        if (process.env.NODE_ENV === 'development') {
        }

        // Remove only the last user message (the malicious one) while preserving legitimate conversation
        setTimeout(() => {
          setMessages((prevMessages) => {
            // Find the last user message index
            let lastUserMessageIndex = -1;
            for (let i = prevMessages.length - 1; i >= 0; i--) {
              if (prevMessages[i].role === 'user') {
                lastUserMessageIndex = i;
                break;
              }
            }

            // Filter out the last user message (the malicious one that caused the error)
            const filteredMessages = prevMessages.filter((_msg, index) => {
              return index !== lastUserMessageIndex;
            });

            if (process.env.NODE_ENV === 'development') {
            }

            return filteredMessages;
          });

          // Update localStorage with filtered messages
          try {
            const currentMessages = JSON.parse(
              localStorage.getItem(STORAGE_KEY) || '[]'
            );

            // Find the last user message index in stored messages
            let lastUserMessageIndex = -1;
            for (let i = currentMessages.length - 1; i >= 0; i--) {
              if (currentMessages[i].role === 'user') {
                lastUserMessageIndex = i;
                break;
              }
            }

            const filteredStoredMessages = currentMessages.filter(
              (_msg: any, index: number) => {
                return index !== lastUserMessageIndex;
              }
            );
            localStorage.setItem(
              STORAGE_KEY,
              JSON.stringify(filteredStoredMessages)
            );
          } catch (_e) {
            // Ignore localStorage errors
          }
        }, 100);
      }

      // Only show toast notification for non-validation errors
      // Validation errors are better displayed in the chat UI error box
      if (!isValidationError) {
        showError('AiResponseFailed');
      }
    },
    onFinish: () => {
      // Clear any previous errors when a message is successfully processed
      // Note: The error state is managed by useChat internally, but we can track success
    },
  });

  // Track error state to manage UI display
  const [showErrorUI, setShowErrorUI] = useState(false);

  // Update error UI state when error changes
  useEffect(() => {
    setShowErrorUI(!!error);
  }, [error]);

  // Auto-resize textarea function
  const autoResizeTextarea = useCallback((textarea: HTMLTextAreaElement) => {
    // Reset height to auto to get the correct scrollHeight
    textarea.style.height = 'auto';
    // Set height to scrollHeight to fit content
    textarea.style.height = `${textarea.scrollHeight}px`;
  }, []);

  // Clear error UI when user starts typing or sends a new message
  const handleInputChangeWithErrorClear = useCallback(
    (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      handleInputChange(e);
      if (showErrorUI) {
        setShowErrorUI(false);
      }
      // Auto-resize the textarea
      autoResizeTextarea(e.target);
    },
    [handleInputChange, showErrorUI, autoResizeTextarea]
  );

  const _handleSubmitWithErrorClear = useCallback(
    (e: React.FormEvent<HTMLFormElement>) => {
      setShowErrorUI(false); // Clear error UI when submitting new message
      handleSubmit(e);
    },
    [handleSubmit]
  );

  // UI state management (preserved from original)
  const [showHelp, setShowHelp] = useState(false);
  const [hasStartedChat, setHasStartedChat] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);
  const [isUserScrolling, setIsUserScrolling] = useState(false);
  const [copiedMessages, setCopiedMessages] = useState<Set<string>>(new Set());
  const [inputAreaHeight, setInputAreaHeight] = useState(128); // Dynamic input area height

  // Character count constants
  const MAX_CHARACTERS = 12_000;
  const SHOW_COUNT_THRESHOLD = 11_000; // Show count when approaching limit

  // Character count display logic
  const getCharacterCountDisplay = useCallback(() => {
    const currentLength = input.length;

    // Only show when approaching limit
    if (currentLength >= SHOW_COUNT_THRESHOLD) {
      const remaining = MAX_CHARACTERS - currentLength;
      const isNearLimit = remaining <= 1000;
      const isAtLimit = remaining <= 100;

      return {
        show: true,
        text: `${currentLength}/${MAX_CHARACTERS}`,
        className: isAtLimit
          ? 'text-red-500'
          : isNearLimit
            ? 'text-orange-500'
            : 'text-muted-foreground',
      };
    }

    return { show: false, text: '', className: '' };
  }, [input.length]);

  // Message deletion state management
  const [isDeleteMode, setIsDeleteMode] = useState(false);
  const [selectedMessageIds, setSelectedMessageIds] = useState<Set<string>>(
    new Set()
  );
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [messageToDelete, setMessageToDelete] = useState<string | null>(null);
  const [bulkDeleteCount, setBulkDeleteCount] = useState(0);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const inputAreaRef = useRef<HTMLDivElement>(null); // For measuring input area height
  const scrollTimeoutRef = useRef<number | null>(null);
  const copyTimeoutsRef = useRef<Map<string, number>>(new Map());

  // Performance monitoring and cleanup (preserved from original)
  const MAX_MESSAGES = 75; // Increased to match enhanced token capacity (2025 optimisation)
  const CLEANUP_INTERVAL = 5 * 60 * 1000; // 5 minutes
  const cleanupIntervalRef = useRef<number | null>(null);

  // PROFESSIONAL: Memoize timestamp formatting function at component level
  const formatTimestamp = useCallback((timestamp?: Date | string) => {
    if (!timestamp) {
      return 'Now';
    }
    try {
      const date = timestamp instanceof Date ? timestamp : new Date(timestamp);
      return date.toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch {
      return 'Now';
    }
  }, []);

  // PROFESSIONAL: Memoize current time for loading/error states
  const currentTimeString = useMemo(() => {
    return new Date().toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit',
    });
  }, []); // Empty dependency array - only calculate once per component mount

  // Track message count for performance monitoring
  const trackMessageCount = useCallback((count: number) => {
    if (process.env.NODE_ENV === 'development' && count > MAX_MESSAGES * 0.8) {
    }
  }, []);

  // Save messages to localStorage for persistence
  const saveMessagesToStorage = useCallback(
    (messagesToSave: typeof messages) => {
      if (typeof window === 'undefined') {
        return;
      }
      try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(messagesToSave));
      } catch (_error) {
        if (process.env.NODE_ENV === 'development') {
        }
      }
    },
    []
  );

  // Save chat state to localStorage for persistence
  const saveChatStateToStorage = useCallback((chatState: boolean) => {
    if (typeof window === 'undefined') {
      return;
    }
    try {
      localStorage.setItem(CHAT_STATE_KEY, JSON.stringify(chatState));
    } catch (_error) {
      if (process.env.NODE_ENV === 'development') {
      }
    }
  }, []);

  // Handle mounting and initial chat state loading to prevent hydration mismatch
  useEffect(() => {
    setIsMounted(true);
    setHasStartedChat(initialChatState);
  }, [initialChatState]);

  // Update chat state based on actual messages loaded by AI SDK
  useEffect(() => {
    // Only update if we're mounted and the AI SDK has had a chance to load messages
    if (isMounted) {
      const shouldShowChat = messages.length > 0;
      if (shouldShowChat !== hasStartedChat) {
        setHasStartedChat(shouldShowChat);
      }
    }
  }, [messages.length, hasStartedChat, isMounted]);

  // PERFORMANCE FIX: Debounced auto-save to prevent lag during typing
  useEffect(() => {
    if (messages.length > 0) {
      // Debounce localStorage saves to prevent blocking during rapid input
      const saveTimeout = setTimeout(() => {
        saveMessagesToStorage(messages);
      }, 500); // Save after 500ms of inactivity

      return () => clearTimeout(saveTimeout);
    }
  }, [messages, saveMessagesToStorage]);

  // Auto-save chat state when it changes
  useEffect(() => {
    saveChatStateToStorage(hasStartedChat);
  }, [hasStartedChat, saveChatStateToStorage]);

  // Auto-scroll to bottom when new messages arrive
  const scrollToBottom = useCallback((smooth = true) => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({
        behavior: smooth ? 'smooth' : 'auto',
        block: 'end',
        inline: 'nearest',
      });
    }
  }, []);

  // Check if user is at bottom of scroll area
  const checkScrollPosition = useCallback(() => {
    if (scrollAreaRef.current) {
      const scrollContainer = scrollAreaRef.current.querySelector(
        '[data-radix-scroll-area-viewport]'
      );
      if (scrollContainer) {
        const { scrollTop, scrollHeight, clientHeight } = scrollContainer;
        const isAtBottom = scrollHeight - scrollTop - clientHeight < 50; // 50px threshold
        setShowScrollToBottom(!isAtBottom && messages.length > 0);
        setIsUserScrolling(!isAtBottom);
      }
    }
  }, [messages.length]);

  // Auto-scroll to bottom when new messages arrive (only if user is not scrolling up)
  useEffect(() => {
    if (messages.length > 0 && !isUserScrolling) {
      const animationFrameId = requestAnimationFrame(() => {
        scrollToBottom(true);
      });
      return () => {
        cancelAnimationFrame(animationFrameId);
      };
    }
  }, [messages, isUserScrolling, scrollToBottom]);

  // Optimized scroll handler with requestAnimationFrame
  const debouncedCheckScrollPosition = useCallback(() => {
    if (scrollTimeoutRef.current) {
      cancelAnimationFrame(scrollTimeoutRef.current);
    }
    scrollTimeoutRef.current = requestAnimationFrame(() => {
      checkScrollPosition();
      scrollTimeoutRef.current = null;
    }) as unknown as number;
  }, [checkScrollPosition]);

  // Set up scroll event listener with proper cleanup
  useEffect(() => {
    const scrollContainer = scrollAreaRef.current?.querySelector(
      '[data-radix-scroll-area-viewport]'
    );
    if (scrollContainer) {
      scrollContainer.addEventListener('scroll', debouncedCheckScrollPosition, {
        passive: true,
      });
      return () => {
        scrollContainer.removeEventListener(
          'scroll',
          debouncedCheckScrollPosition
        );
        if (scrollTimeoutRef.current) {
          cancelAnimationFrame(scrollTimeoutRef.current);
          scrollTimeoutRef.current = null;
        }
      };
    }
  }, [debouncedCheckScrollPosition]);

  // Focus input on mount and auto-resize
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
      autoResizeTextarea(inputRef.current);
    }
  }, [autoResizeTextarea]);

  // Auto-resize textarea when input value changes programmatically
  useEffect(() => {
    if (inputRef.current) {
      autoResizeTextarea(inputRef.current);
    }
  }, [autoResizeTextarea]);

  // Dynamic input area height tracking with ResizeObserver
  useEffect(() => {
    if (!inputAreaRef.current) {
      return;
    }

    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const height = entry.contentRect.height;
        setInputAreaHeight(height);
      }
    });

    resizeObserver.observe(inputAreaRef.current);

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  // Message cleanup function to prevent memory issues
  const cleanupOldMessages = useCallback(() => {
    if (messages.length > MAX_MESSAGES) {
      // Keep the most recent messages
      const recentMessages = messages.slice(-MAX_MESSAGES);
      setMessages(recentMessages);
    }

    // Clean up copied messages set
    setCopiedMessages(new Set<string>());
  }, [messages, setMessages]);

  // Set up periodic cleanup
  useEffect(() => {
    cleanupIntervalRef.current = setInterval(() => {
      cleanupOldMessages();

      // Force garbage collection hint (if available)
      if (typeof window !== 'undefined' && 'gc' in window) {
        try {
          (window as { gc?: () => void }).gc?.();
        } catch {
          // Ignore if gc is not available
        }
      }
    }, CLEANUP_INTERVAL) as unknown as number;

    return () => {
      if (cleanupIntervalRef.current) {
        clearInterval(cleanupIntervalRef.current);
      }
    };
  }, [cleanupOldMessages]);

  // Cleanup copy timeouts on unmount
  useEffect(() => {
    // Capture the current ref value when the effect runs
    const timeouts = copyTimeoutsRef.current;

    return () => {
      // Use the captured value in cleanup
      timeouts.forEach((timeout) => clearTimeout(timeout));
      timeouts.clear();
    };
  }, []);

  // Handle form submission with AI SDK
  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!input.trim() || isAILoading) {
      return;
    }

    // Clear error UI when submitting new message
    setShowErrorUI(false);

    // Transition to chat state on first message
    if (!hasStartedChat) {
      setHasStartedChat(true);
    }

    // Track message count for performance monitoring
    trackMessageCount(messages.length + 1);

    // Trigger cleanup if we're approaching the limit
    if (messages.length > MAX_MESSAGES * 0.8) {
      setTimeout(() => cleanupOldMessages(), 100);
    }

    // Submit to AI SDK
    handleSubmit(e);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage(e as unknown as React.FormEvent);
    }
  };

  const clearConversation = () => {
    setMessages([]);
    setInput('');
    setHasStartedChat(false);

    // Clear localStorage
    if (typeof window !== 'undefined') {
      try {
        localStorage.removeItem(STORAGE_KEY);
        localStorage.removeItem(CHAT_STATE_KEY);
      } catch (_error) {
        // Silently handle localStorage errors
      }
    }

    // Reset scroll state
    setIsUserScrolling(false);
    setShowScrollToBottom(false);

    // Clear copied messages
    setCopiedMessages(new Set());

    // Force cleanup
    cleanupOldMessages();
  };

  const handleHelpQuerySelect = (query: string) => {
    setInput(query);

    setShowHelp(false);
    inputRef.current?.focus();
  };

  const handleCopyMessage = async (messageId: string, content: string) => {
    try {
      await navigator.clipboard.writeText(content);

      // Add message ID to copied set to show check icon
      setCopiedMessages((prev) => new Set(prev).add(messageId));

      // Clear any existing timeout for this message
      const existingTimeout = copyTimeoutsRef.current.get(messageId);
      if (existingTimeout) {
        clearTimeout(existingTimeout);
      }

      // Remove from copied set after 2 seconds to revert to copy icon
      const timeout = setTimeout(() => {
        setCopiedMessages((prev) => {
          const newSet = new Set(prev);
          newSet.delete(messageId);
          return newSet;
        });
        copyTimeoutsRef.current.delete(messageId);
      }, 2000) as unknown as number;
      copyTimeoutsRef.current.set(messageId, timeout);
    } catch (_error) {
      if (process.env.NODE_ENV === 'development') {
      }
      showError('MessageCopyFailed');
    }
  };

  /**
   * Message deletion handlers
   * Implements industry-standard confirmation flow for destructive actions
   */

  /**
   * Initiates single message deletion with confirmation dialogue
   * @param messageId - Unique identifier of the message to delete
   */
  const handleDeleteMessage = useCallback((messageId: string) => {
    setMessageToDelete(messageId);
    setBulkDeleteCount(1);
    setIsDeleteDialogOpen(true);
  }, []);

  /**
   * Initiates bulk message deletion with confirmation dialogue
   * Uses selected messages from delete mode
   */
  const handleBulkDeleteMessages = useCallback(() => {
    setBulkDeleteCount(selectedMessageIds.size);
    setIsDeleteDialogOpen(true);
  }, [selectedMessageIds.size]);

  /**
   * Confirms and executes message deletion
   * Handles both single and bulk deletion with proper state management
   * Updates AI SDK state and localStorage persistence
   */
  const confirmDeleteMessages = useCallback(() => {
    try {
      if (messageToDelete) {
        // Single message deletion
        const updatedMessages = messages.filter(
          (msg) => msg.id !== messageToDelete
        );
        setMessages(updatedMessages);
        saveMessagesToStorage(updatedMessages);
        showSuccess('messageDeleted');
        setMessageToDelete(null);
      } else if (selectedMessageIds.size > 0) {
        // Bulk message deletion
        const updatedMessages = messages.filter(
          (msg) => !selectedMessageIds.has(msg.id)
        );
        setMessages(updatedMessages);
        saveMessagesToStorage(updatedMessages);
        showSuccess('messagesDeleted');
        setSelectedMessageIds(new Set());
        setIsDeleteMode(false);
      }

      setIsDeleteDialogOpen(false);
      setBulkDeleteCount(0);
    } catch (_error) {
      if (process.env.NODE_ENV === 'development') {
      }
      showError('MessageDeleteFailed');
    }
  }, [
    messageToDelete,
    selectedMessageIds,
    messages,
    setMessages,
    saveMessagesToStorage,
    showError,
    showSuccess,
  ]);

  /**
   * Cancels message deletion and resets dialogue state
   */
  const cancelDeleteMessages = useCallback(() => {
    setIsDeleteDialogOpen(false);
    setMessageToDelete(null);
    setBulkDeleteCount(0);
  }, []);

  /**
   * Selection mode handlers for bulk operations
   * Implements Poe.com-style selection interface
   */

  /**
   * Toggles between normal and selection mode
   * Clears any existing selections when entering selection mode
   */
  const toggleDeleteMode = useCallback(() => {
    setIsDeleteMode(!isDeleteMode);
    setSelectedMessageIds(new Set());
  }, [isDeleteMode]);

  /**
   * Handles individual message selection in delete mode
   * @param messageId - ID of the message to select/deselect
   * @param checked - Whether the message should be selected
   */
  const handleSelectMessage = useCallback(
    (messageId: string, checked: boolean) => {
      setSelectedMessageIds((prev) => {
        const newSet = new Set(prev);
        if (checked) {
          newSet.add(messageId);
        } else {
          newSet.delete(messageId);
        }
        return newSet;
      });
    },
    []
  );

  /**
   * Handles select all/deselect all functionality
   * @param checked - Whether to select or deselect all messages
   */
  const handleSelectAllMessages = useCallback(
    (checked: boolean) => {
      if (checked) {
        const allMessageIds = new Set(messages.map((msg) => msg.id));
        setSelectedMessageIds(allMessageIds);
      } else {
        setSelectedMessageIds(new Set());
      }
    },
    [messages]
  );

  const handleRegenerateMessage = async () => {
    try {
      // Use AI SDK's reload function to regenerate the last response
      await reload();
    } catch (_error) {
      if (process.env.NODE_ENV === 'development') {
      }
      showError('ResponseRegenerationFailed');
    }
  };

  // Prevent hydration mismatch by not rendering until mounted
  if (!isMounted) {
    return null;
  }

  // Render initial state (Grok-style landing)
  if (!hasStartedChat) {
    return (
      <div className="relative h-full">
        {/* Help Dialog */}
        <ChatbotHelpDialog
          onOpenChange={setShowHelp}
          onQuerySelect={handleHelpQuerySelect}
          open={showHelp}
        />

        {/* Initial State - Full width, independent of help panel */}
        <div className="relative flex h-full flex-col">
          {/* Header Section - Absolute positioned to not affect centring */}
          <div className="absolute top-0 right-0 z-10 p-3 sm:p-4">
            <button
              aria-label={t('showHelpAndQueryExamples')}
              className="flex h-11 min-h-[44px] w-11 min-w-[44px] cursor-pointer touch-manipulation items-center justify-center rounded-full text-[#97A4FF] transition-colors hover:bg-[#97A4FF]/10 hover:text-[#97A4FF] focus:outline-none sm:h-10 sm:w-10"
              onClick={() => setShowHelp(!showHelp)}
              title={t('help')}
            >
              <HelpCircle className="h-5 w-5 sm:h-4 sm:w-4" />
            </button>
          </div>

          {/* Centred Content Area - Full height centring */}
          <div className="flex min-h-full flex-1 items-center justify-center bg-background">
            <div className="w-full max-w-2xl px-4 sm:px-6">
              {/* Fluid Icon */}
              <div className="mb-4 text-center">
                <FluidIcon size="md" />
              </div>

              {/* Ask August Title */}
              <div className="mb-6 text-center sm:mb-8">
                <h1 className="mb-3 font-bold text-3xl text-slate-900 sm:mb-4 sm:text-4xl lg:text-5xl dark:text-slate-100">
                  {t('askAugust')}
                </h1>
                <p className="mx-auto max-w-md text-base text-slate-600 sm:text-lg dark:text-slate-400">
                  {t('analyticsAssistantForCensusData')}
                </p>
              </div>

              {/* Centered Chat Input */}
              <form className="mb-4 sm:mb-6" onSubmit={handleSendMessage}>
                <div
                  className="cursor-text rounded-2xl border border-border bg-background shadow-lg transition-all duration-200 focus-within:border-[#97A4FF] focus-within:ring-2 focus-within:ring-[#97A4FF]/20 hover:border-border/80"
                  onClick={() => inputRef.current?.focus()}
                >
                  <div className="px-3 pt-3 pb-2 sm:px-4 sm:pt-4">
                    <textarea
                      aria-describedby="character-count-landing"
                      aria-label="Enter your question about census data"
                      className="max-h-[100px] min-h-[24px] w-full resize-none overflow-y-auto border-0 bg-transparent text-base leading-relaxed placeholder:text-slate-500 focus:outline-none focus:ring-0 sm:max-h-[120px] sm:text-sm dark:placeholder:text-slate-400"
                      disabled={isAILoading}
                      maxLength={12_000}
                      onChange={handleInputChangeWithErrorClear}
                      onKeyDown={handleKeyDown}
                      placeholder={t('askMeAnythingAboutYourCensusD')}
                      ref={inputRef}
                      rows={1}
                      style={{
                        height: 'auto',
                        minHeight: '24px',
                        fontSize: '16px', // Prevent zoom on iOS
                      }}
                      value={input}
                    />
                  </div>
                  <div className="flex items-center justify-between px-3 pb-2 sm:px-4 sm:pb-3">
                    <div
                      aria-live="polite"
                      className="text-xs"
                      id="character-count-landing"
                    >
                      {(() => {
                        const charCount = getCharacterCountDisplay();
                        return charCount.show ? (
                          <span className={charCount.className}>
                            {charCount.text}
                          </span>
                        ) : null;
                      })()}
                    </div>
                    <Button
                      aria-label={
                        isAILoading ? t('sendingMessage') : t('sendMessage')
                      }
                      className="h-11 min-h-[44px] w-11 min-w-[44px] touch-manipulation rounded-full bg-[#97A4FF] p-0 text-white hover:bg-[#97A4FF]/90 focus:outline-none focus:ring-2 focus:ring-[#97A4FF]/50 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 sm:h-10 sm:w-10"
                      disabled={isAILoading || !input.trim()}
                      size="sm"
                      type="submit"
                    >
                      {isAILoading ? (
                        <div className="h-6 w-6 animate-spin rounded-full border border-white border-t-transparent sm:h-5 sm:w-5" />
                      ) : (
                        <ArrowUp className="!h-7 !w-7 sm:!h-6 sm:!w-6" />
                      )}
                    </Button>
                  </div>
                </div>
              </form>

              {/* Quick Action Buttons */}
              <div className="flex flex-wrap justify-center gap-2 sm:gap-3">
                <Button
                  aria-label="Ask about total member count"
                  className="h-9 touch-manipulation border-[#3B82F6]/20 px-3 text-[#3B82F6] text-xs hover:bg-[#3B82F6]/10 hover:text-[#3B82F6] focus:outline-none focus:ring-2 focus:ring-[#3B82F6] focus:ring-offset-2 sm:h-8 sm:px-4 sm:text-sm"
                  disabled={isAILoading}
                  onClick={() => {
                    setInput(tCommon('quickActionMemberCount'));
                    setTimeout(() => inputRef.current?.focus(), 0);
                  }}
                  size="sm"
                  variant="outline"
                >
                  <Users className="mr-1 h-4 w-4 sm:mr-2" />
                  <span className="whitespace-nowrap">
                    {t('chatbotMemberCount')}
                  </span>
                </Button>
                <Button
                  aria-label="Ask for gender distribution chart"
                  className="h-9 touch-manipulation border-[#10B981]/20 px-3 text-[#10B981] text-xs hover:bg-[#10B981]/10 hover:text-[#10B981] focus:outline-none focus:ring-2 focus:ring-[#10B981] focus:ring-offset-2 sm:h-8 sm:px-4 sm:text-sm"
                  disabled={isAILoading}
                  onClick={() => {
                    setInput(tCommon('quickActionGenderChart'));
                    setTimeout(() => inputRef.current?.focus(), 0);
                  }}
                  size="sm"
                  variant="outline"
                >
                  <Home className="mr-1 h-4 w-4 sm:mr-2" />
                  <span className="whitespace-nowrap">{t('genderChart')}</span>
                </Button>
                <Button
                  aria-label="Ask about unique code availability"
                  className="h-9 touch-manipulation border-[#FF6308]/20 px-3 text-[#FF6308] text-xs hover:bg-[#FF6308]/10 hover:text-[#FF6308] focus:outline-none focus:ring-2 focus:ring-[#FF6308] focus:ring-offset-2 sm:h-8 sm:px-4 sm:text-sm"
                  disabled={isAILoading}
                  onClick={() => {
                    setInput(tCommon('quickActionCodeStatus'));
                    setTimeout(() => inputRef.current?.focus(), 0);
                  }}
                  size="sm"
                  variant="outline"
                >
                  <Scroll className="mr-1 h-4 w-4 sm:mr-2" />
                  <span className="whitespace-nowrap">
                    {t('chatbotCodeStatus')}
                  </span>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Render chat state (current layout without quick action buttons)
  return (
    <div className="relative h-full">
      {/* Help Dialog */}
      <ChatbotHelpDialog
        onOpenChange={setShowHelp}
        onQuerySelect={handleHelpQuerySelect}
        open={showHelp}
      />

      {/* Main Chat Area - Full width, independent of help panel */}
      <div className="flex h-full flex-col">
        {/* Header Section - Chat controls */}
        <div className="flex items-center justify-between bg-background px-3 py-2 sm:px-4 sm:py-3">
          {/* Left side - Delete mode controls */}
          <div className="flex items-center gap-2">
            {messages.length > 0 && (
              <>
                {/* Delete mode toggle */}
                <button
                  aria-label={
                    isDeleteMode
                      ? tCommon('exitDeleteMode')
                      : tCommon('enterDeleteMode')
                  }
                  className={`flex h-10 min-h-[40px] w-10 min-w-[40px] cursor-pointer touch-manipulation items-center justify-center rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 sm:h-9 sm:w-9 ${
                    isDeleteMode
                      ? 'bg-red-500/15 text-red-600 hover:bg-red-500/25 focus:ring-red-500/50'
                      : 'text-red-500 hover:bg-red-500/10 hover:text-red-600 focus:ring-red-500/50'
                  }`}
                  onClick={toggleDeleteMode}
                  title={
                    isDeleteMode
                      ? tCommon('exitDeleteMode')
                      : t('deleteMessage')
                  }
                >
                  {isDeleteMode ? (
                    <CheckSquare className="h-4 w-4 sm:h-3.5 sm:w-3.5" />
                  ) : (
                    <Trash2 className="h-4 w-4 sm:h-3.5 sm:w-3.5" />
                  )}
                </button>

                {/* Selection controls (visible in delete mode) */}
                {isDeleteMode && (
                  <>
                    <button
                      className="rounded-md px-2 py-1 text-muted-foreground text-sm transition-colors hover:bg-muted hover:text-foreground"
                      onClick={() =>
                        handleSelectAllMessages(
                          selectedMessageIds.size !== messages.length
                        )
                      }
                      title={
                        selectedMessageIds.size === messages.length
                          ? t('deselectAllMessages')
                          : t('selectAllMessages')
                      }
                    >
                      {selectedMessageIds.size === messages.length
                        ? t('deselectAll')
                        : t('selectAll')}
                    </button>

                    {selectedMessageIds.size > 0 && (
                      <button
                        className="rounded-md bg-red-500 px-2 py-1 text-sm text-white transition-colors hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500/50 focus:ring-offset-2"
                        onClick={handleBulkDeleteMessages}
                        title={t('deleteSelectedMessages', {
                          count: selectedMessageIds.size.toString(),
                          plural: selectedMessageIds.size > 1 ? 's' : '',
                        })}
                      >
                        {t('deleteCount', {
                          count: selectedMessageIds.size.toString(),
                        })}
                      </button>
                    )}
                  </>
                )}
              </>
            )}
          </div>

          {/* Right side - Utility controls */}
          <div className="flex gap-1 sm:gap-2">
            <button
              aria-label={t('showHelpAndQueryExamples')}
              className="flex h-10 min-h-[40px] w-10 min-w-[40px] cursor-pointer touch-manipulation items-center justify-center rounded-full text-[#97A4FF] transition-colors hover:bg-[#97A4FF]/10 hover:text-[#97A4FF] focus:outline-none focus:ring-2 focus:ring-[#97A4FF]/50 focus:ring-offset-2 sm:h-9 sm:w-9"
              onClick={() => setShowHelp(!showHelp)}
              title={t('help')}
            >
              <HelpCircle className="h-4 w-4 sm:h-3.5 sm:w-3.5" />
            </button>
            <button
              aria-label={t('clearConversation')}
              className="flex h-10 min-h-[40px] w-10 min-w-[40px] cursor-pointer touch-manipulation items-center justify-center rounded-full text-[#FF6308] transition-colors hover:bg-[#FF6308]/10 hover:text-[#FF6308] focus:outline-none focus:ring-2 focus:ring-[#FF6308]/50 focus:ring-offset-2 sm:h-9 sm:w-9"
              onClick={clearConversation}
              title={t('clearConversation')}
            >
              <MessageSquare className="h-4 w-4 sm:h-3.5 sm:w-3.5" />
            </button>
          </div>
        </div>

        {/* Main Chat Interface - Fixed Layout Structure */}
        <div className="relative min-h-0 flex-1 bg-background">
          {/* Messages Area - Takes remaining space above fixed input */}
          <div
            className="absolute inset-0"
            style={{ bottom: `${inputAreaHeight + 16}px` }} // Dynamic bottom spacing with 16px buffer
          >
            <ScrollArea className="h-full" ref={scrollAreaRef}>
              <div
                className="mx-auto max-w-4xl space-y-3 px-3 py-3 sm:space-y-4 sm:px-4 sm:py-4"
                ref={messagesContainerRef}
              >
                {messages.map((message, index) => {
                  // PERFORMANCE: Calculate if this is the last assistant message efficiently
                  const isLastAssistantMessage =
                    message.role === 'assistant' &&
                    index === messages.length - 1;

                  // PERFORMANCE: Use memoized timestamp formatting function
                  const formattedTime = formatTimestamp(message.createdAt);

                  return (
                    <div
                      className={`flex ${
                        message.role === 'user'
                          ? 'justify-end'
                          : 'justify-start'
                      }`}
                      key={message.id}
                    >
                      {/* Message Content */}
                      <div
                        className={`flex w-full flex-col ${
                          message.role === 'user'
                            ? 'max-w-[85%] items-end sm:max-w-[75%] md:max-w-[70%]'
                            : 'max-w-full items-start'
                        }`}
                      >
                        {/* Message Bubble */}
                        <div
                          className={`w-full max-w-full overflow-hidden ${
                            message.role === 'user'
                              ? 'rounded-2xl rounded-br-md border border-border bg-muted px-3 py-2 text-foreground sm:px-4 sm:py-3'
                              : ''
                          }`}
                        >
                          {message.role === 'user' ? (
                            <div className="whitespace-pre-wrap leading-relaxed">
                              {message.content}
                            </div>
                          ) : (
                            <RichMessageRenderer
                              content={message.content}
                              queryType="general"
                            />
                          )}
                        </div>

                        {/* Message Metadata - Below message */}
                        <div
                          className={`mt-1 flex items-center gap-1 sm:gap-2 ${
                            message.role === 'user'
                              ? 'flex-row-reverse'
                              : 'flex-row'
                          }`}
                        >
                          {/* Selection checkbox in delete mode */}
                          {isDeleteMode && (
                            <Checkbox
                              aria-label={`Select message from ${message.role}`}
                              checked={selectedMessageIds.has(message.id)}
                              className="mr-1"
                              onCheckedChange={(checked) =>
                                handleSelectMessage(
                                  message.id,
                                  checked as boolean
                                )
                              }
                            />
                          )}

                          {/* Copy button */}
                          <button
                            aria-label={
                              copiedMessages.has(message.id)
                                ? t('messageCopied')
                                : t('copyMessage')
                            }
                            className="flex h-8 min-h-[32px] w-8 min-w-[32px] cursor-pointer touch-manipulation items-center justify-center rounded-full text-slate-500 transition-all duration-200 hover:bg-slate-500/10 hover:text-slate-600 focus:outline-none sm:h-7 sm:w-7 dark:text-slate-400 dark:hover:text-slate-300"
                            onClick={() =>
                              handleCopyMessage(message.id, message.content)
                            }
                            title={
                              copiedMessages.has(message.id)
                                ? t('messageCopied')
                                : t('copyMessage')
                            }
                          >
                            {copiedMessages.has(message.id) ? (
                              <Check className="h-3.5 w-3.5 text-green-600" />
                            ) : (
                              <Copy className="h-3.5 w-3.5 text-slate-400 dark:text-slate-500" />
                            )}
                          </button>

                          {/* Delete button */}
                          {!isDeleteMode && (
                            <button
                              aria-label={t('deleteMessage')}
                              className="flex h-8 min-h-[32px] w-8 min-w-[32px] cursor-pointer touch-manipulation items-center justify-center rounded-full text-red-500 transition-all duration-200 hover:bg-red-500/10 hover:text-red-600 focus:outline-none sm:h-7 sm:w-7 dark:text-red-400 dark:hover:text-red-300"
                              onClick={() => handleDeleteMessage(message.id)}
                              title={t('deleteMessage')}
                            >
                              <Trash2 className="h-3.5 w-3.5" />
                            </button>
                          )}

                          {/* PERFORMANCE FIX: Use index comparison instead of indexOf */}
                          {isLastAssistantMessage && (
                            <button
                              aria-label={
                                isAILoading
                                  ? t('regeneratingResponse')
                                  : t('regenerateResponse')
                              }
                              className="flex h-8 min-h-[32px] w-8 min-w-[32px] cursor-pointer touch-manipulation items-center justify-center rounded-full text-slate-500 transition-all duration-200 hover:bg-slate-500/10 hover:text-slate-600 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50 sm:h-7 sm:w-7 dark:text-slate-400 dark:hover:text-slate-300"
                              disabled={isAILoading}
                              onClick={handleRegenerateMessage}
                              title={
                                isAILoading
                                  ? t('regenerating')
                                  : t('regenerateResponse')
                              }
                            >
                              <RefreshCw
                                className={`h-3.5 w-3.5 text-slate-400 dark:text-slate-500 ${isAILoading ? 'animate-spin' : ''}`}
                              />
                            </button>
                          )}

                          <span className="text-muted-foreground text-xs">
                            {formattedTime}
                          </span>
                        </div>
                      </div>
                    </div>
                  );
                })}

                {isAILoading && (
                  <div className="flex justify-start">
                    <div className="flex w-full max-w-full flex-col items-start overflow-hidden">
                      {/* Enhanced typing indicator with animation */}
                      <div className="rounded-2xl rounded-bl-md border border-border/50 bg-muted/50 px-4 py-3">
                        <div className="flex items-center gap-2">
                          <div className="flex gap-1">
                            <div
                              className="h-2 w-2 animate-bounce rounded-full bg-[#97A4FF]"
                              style={{ animationDelay: '0ms' }}
                            />
                            <div
                              className="h-2 w-2 animate-bounce rounded-full bg-[#97A4FF]"
                              style={{ animationDelay: '150ms' }}
                            />
                            <div
                              className="h-2 w-2 animate-bounce rounded-full bg-[#97A4FF]"
                              style={{ animationDelay: '300ms' }}
                            />
                          </div>
                          <span className="text-muted-foreground text-sm">
                            {t('augustIsThinking')}
                          </span>
                        </div>
                      </div>
                      <div className="mt-1 flex items-center gap-2">
                        <span className="text-muted-foreground text-xs">
                          {currentTimeString}
                        </span>
                        <Badge
                          className="border-[#97A4FF]/20 bg-[#97A4FF]/10 text-[#97A4FF] text-xs"
                          variant="outline"
                        >
                          <MessageSquare className="mr-1 h-3 w-3" />
                          <span>{t('aiProcessing')}</span>
                        </Badge>
                      </div>
                    </div>
                  </div>
                )}

                {/* Enhanced error display */}
                {showErrorUI && error && (
                  <div className="flex justify-start">
                    <div className="flex w-full max-w-full flex-col items-start overflow-hidden">
                      <div className="rounded-2xl rounded-bl-md border border-red-200 bg-red-50 px-4 py-3 dark:border-red-800 dark:bg-red-950/20">
                        <div className="flex items-start gap-3">
                          <AlertCircle className="mt-0.5 h-5 w-5 flex-shrink-0 text-red-500" />
                          <div className="flex-1">
                            <div className="mb-1 font-medium text-red-800 text-sm dark:text-red-200">
                              {t('connectionError')}
                            </div>
                            <div className="text-red-700 text-sm dark:text-red-300">
                              {error.message}
                            </div>
                            <div className="mt-2 text-red-600 text-xs dark:text-red-400">
                              {t('connectionErrorDescription')}
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="mt-1 flex items-center gap-2">
                        <span className="text-muted-foreground text-xs">
                          {currentTimeString}
                        </span>
                        <Badge
                          className="border-red-200 bg-red-50 text-red-600 text-xs dark:border-red-800 dark:bg-red-950/20 dark:text-red-400"
                          variant="outline"
                        >
                          <AlertCircle className="mr-1 h-3 w-3" />
                          <span>{tCommon('error')}</span>
                        </Badge>
                      </div>
                    </div>
                  </div>
                )}

                {/* Invisible element to scroll to */}
                <div className="h-1" ref={messagesEndRef} />
              </div>
            </ScrollArea>
          </div>

          {/* Scroll to Bottom Button */}
          {showScrollToBottom && (
            <div
              className="-translate-x-1/2 absolute left-1/2 z-10 transform"
              style={{ bottom: `${inputAreaHeight + 24}px` }} // Dynamic positioning above input area
            >
              <Button
                aria-label={t('scrollToBottom')}
                className="h-11 min-h-[44px] w-11 min-w-[44px] touch-manipulation rounded-full border border-border bg-background p-0 text-foreground hover:bg-muted focus:outline-none focus:ring-2 focus:ring-[#97A4FF] focus:ring-offset-2 sm:h-10 sm:w-10"
                onClick={() => scrollToBottom(true)}
                size="sm"
                title={t('scrollToBottom')}
              >
                <ArrowDown className="h-5 w-5 sm:h-4 sm:w-4" />
              </Button>
            </div>
          )}

          {/* Input Area - Absolutely positioned at bottom */}
          <div
            className="absolute right-0 bottom-0 left-0 bg-background"
            ref={inputAreaRef}
          >
            <div className="mx-auto max-w-4xl px-3 py-3 sm:px-4 sm:py-4">
              <form onSubmit={handleSendMessage}>
                <div
                  className="cursor-text rounded-2xl border border-border bg-background shadow-lg transition-all duration-200 focus-within:border-[#97A4FF] focus-within:ring-2 focus-within:ring-[#97A4FF]/20 hover:border-border/80"
                  onClick={() => inputRef.current?.focus()}
                >
                  {/* Top Row - Text Input */}
                  <div className="px-3 pt-3 pb-2 sm:px-4 sm:pt-4">
                    <textarea
                      aria-describedby="character-count"
                      aria-label="Enter your question about census data"
                      className="max-h-[100px] min-h-[24px] w-full resize-none overflow-y-auto border-0 bg-transparent text-base leading-relaxed placeholder:text-muted-foreground focus:outline-none focus:ring-0 sm:max-h-[120px] sm:text-sm"
                      disabled={isAILoading}
                      maxLength={12_000}
                      onChange={handleInputChangeWithErrorClear}
                      onKeyDown={handleKeyDown}
                      placeholder={t('askAboutMembersHouseholdsOrUniqueCodes')}
                      ref={inputRef}
                      rows={1}
                      style={{
                        height: 'auto',
                        minHeight: '24px',
                        fontSize: '16px', // Prevent zoom on iOS
                      }}
                      value={input}
                    />
                  </div>

                  {/* Bottom Row - Action Buttons */}
                  <div className="flex items-center justify-between px-3 pb-2 sm:px-4 sm:pb-3">
                    <div className="flex items-center gap-2">
                      <div
                        aria-live="polite"
                        className="text-xs"
                        id="character-count"
                      >
                        {(() => {
                          const charCount = getCharacterCountDisplay();
                          return charCount.show ? (
                            <span className={charCount.className}>
                              {charCount.text}
                            </span>
                          ) : null;
                        })()}
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <Button
                        aria-label={
                          isAILoading ? t('sendingMessage') : t('sendMessage')
                        }
                        className="flex h-11 min-h-[44px] w-11 min-w-[44px] touch-manipulation items-center justify-center rounded-full bg-[#97A4FF] p-0 text-white transition-all duration-200 hover:bg-[#97A4FF]/90 focus:outline-none focus:ring-2 focus:ring-[#97A4FF]/50 focus:ring-offset-2 disabled:bg-slate-300 disabled:text-slate-500 sm:h-10 sm:w-10"
                        disabled={isAILoading || !input.trim()}
                        size="sm"
                        type="submit"
                      >
                        {isAILoading ? (
                          <div className="h-6 w-6 animate-spin rounded-full border-2 border-white border-t-transparent sm:h-5 sm:w-5" />
                        ) : (
                          <ArrowUp className="!h-7 !w-7 sm:!h-6 sm:!w-6" />
                        )}
                      </Button>
                    </div>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>

      {/* Message Deletion Confirmation Dialog */}
      <AlertDialog
        onOpenChange={setIsDeleteDialogOpen}
        open={isDeleteDialogOpen}
      >
        <AlertDialogContent className="sm:max-w-md">
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <Trash2 className="h-5 w-5 text-red-500" />
              {bulkDeleteCount > 1
                ? t('deleteMessagesTitle')
                : t('deleteMessageTitle')}
            </AlertDialogTitle>
            <AlertDialogDescription className="text-muted-foreground text-sm">
              {bulkDeleteCount > 1
                ? t('deleteMultipleMessagesConfirmation', {
                    count: bulkDeleteCount.toString(),
                  })
                : t('deleteSingleMessageConfirmation')}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2">
            <AlertDialogCancel
              className="mt-2 sm:mt-0"
              onClick={cancelDeleteMessages}
            >
              {tCommon('cancel')}
            </AlertDialogCancel>
            <AlertDialogAction
              className="bg-red-600 text-white hover:bg-red-700 focus:ring-red-600"
              onClick={confirmDeleteMessages}
            >
              <Trash2 className="mr-2 h-4 w-4" />
              {bulkDeleteCount > 1
                ? `${tCommon('delete')} ${bulkDeleteCount} ${t('messages')}`
                : `${tCommon('delete')} ${t('message')}`}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}

// Export the component (temporarily without Error Boundary)
export function AnalyticsChatbotAISDKClient(
  props: AnalyticsChatbotAISDKClientProps
) {
  return <AnalyticsChatbotAISDKClientInner {...props} />;
}
