'use client';

import {
  ChevronDown,
  HelpCircle,
  Home,
  LayoutDashboard,
  Menu,
  Users,
  X,
} from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';
import { LanguageSelector } from '@/components/language/language-selector';
import { ThemeToggle } from '@/components/theme/theme-toggle';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';

/**
 * SessionInfo type definition
 * Contains information about an active session
 */
interface SessionInfo {
  isActive: boolean;
  name: string | null;
  role: string | null;
  returnPath: string;
  icon: React.ElementType;
}

/**
 * SessionAwareNavigation component
 *
 * A dynamic navigation component that adapts based on user authentication status.
 * Detects active sessions and provides contextual navigation options.
 */
export function SessionAwareNavigation() {
  const pathname = usePathname();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [dropdownOpen, setDropdownOpen] = useState(false);

  // Translation hooks
  const t = useTranslations('navigation');
  const tCommon = useTranslations('common');
  const tBrand = useTranslations('brand');

  // Session state
  const [adminSession, setAdminSession] = useState<SessionInfo>({
    isActive: false,
    name: null,
    role: null,
    returnPath: '/admin/dashboard',
    icon: LayoutDashboard,
  });

  const [censusSession, setCensusSession] = useState<SessionInfo>({
    isActive: false,
    name: null,
    role: null,
    returnPath: '', // Will be dynamically set based on the code
    icon: Users,
  });

  const [isLoading, setIsLoading] = useState(true);

  // Check for active sessions
  useEffect(() => {
    const checkSessions = async () => {
      setIsLoading(true);

      try {
        // Check admin session
        const adminResponse = await fetch('/api/auth/session', {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
          // Prevent caching to ensure we get the latest session state
          cache: 'no-store',
        });

        if (adminResponse.ok) {
          const adminData = await adminResponse.json();
          setAdminSession((prev) => ({
            ...prev,
            isActive: !!adminData.user,
            name: adminData.user?.name || null,
            role: adminData.user?.role || null,
          }));
        }
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('Error checking admin session:', error);
        }
      }

      try {
        // Check census session
        const censusResponse = await fetch('/api/census/auth/session', {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
          // Prevent caching to ensure we get the latest session state
          cache: 'no-store',
        });

        if (censusResponse.ok) {
          const censusData = await censusResponse.json();

          // Set the census session with the correct return path based on the code
          setCensusSession((prev) => ({
            ...prev,
            isActive: !!censusData.user,
            name: censusData.user?.name || null,
            role: censusData.user?.role || null,
            // Set the return path to the census page with the user's code
            returnPath: censusData.user?.code
              ? `/census/${censusData.user.code}`
              : '',
          }));
        }
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('Error checking census session:', error);
        }
      }

      setIsLoading(false);
    };

    checkSessions();
  }, []);

  // Handle responsive behaviour
  useEffect(() => {
    const handleResize = () => {
      const isMobile = window.innerWidth < 768; // md breakpoint is 768px

      // Close dropdown if screen is resized to mobile
      if (isMobile && dropdownOpen) {
        setDropdownOpen(false);
      }
    };

    // Initial check
    handleResize();

    // Add resize listener
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, [dropdownOpen]);

  // Toggle mobile menu
  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  /**
   * Close mobile menu when clicking outside
   */
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Only run this if the mobile menu is open
      if (!mobileMenuOpen) {
        return;
      }

      // Check if the click was outside the header
      const header = document.querySelector('header');
      if (header && !header.contains(event.target as Node)) {
        setMobileMenuOpen(false);
      }
    };

    // Add event listener
    document.addEventListener('mousedown', handleClickOutside);

    // Cleanup
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [mobileMenuOpen]);

  // Determine if any sessions are active
  const hasActiveSessions = adminSession.isActive || censusSession.isActive;

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/80 backdrop-blur-sm supports-[backdrop-filter]:bg-background/60">
      <div className="flex h-16 items-center justify-between px-0">
        {/* Logo and Brand */}
        <div className="flex items-center gap-2 pl-4">
          <Link
            className="flex cursor-pointer items-center gap-2 font-medium"
            href="/"
          >
            <div className="flex h-6 w-6 items-center justify-center rounded-md bg-primary text-primary-foreground">
              <svg
                aria-hidden="true"
                fill="none"
                height="16"
                stroke="currentColor"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                viewBox="0 0 24 24"
                width="16"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M3 9h18v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9Z" />
                <path d="M3 9V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v4" />
                <path d="M13 13h4" />
                <path d="M13 17h4" />
                <path d="M7 13h2v4H7z" />
              </svg>
            </div>
            <span className="font-medium sm:inline-block">
              {tBrand('name')}
            </span>
          </Link>
        </div>

        {/* Desktop Navigation */}
        <nav
          aria-label="Main navigation"
          className="hidden items-center gap-6 md:flex"
        >
          <Link
            className={cn(
              'flex cursor-pointer items-center justify-center rounded-md p-2 font-medium text-sm transition-colors hover:bg-accent',
              pathname === '/' ? 'text-primary' : 'text-muted-foreground'
            )}
            href="/"
          >
            <Home aria-hidden="true" className="mr-2 h-4 w-4" />
            {t('home')}
          </Link>
          <Link
            className={cn(
              'flex cursor-pointer items-center justify-center rounded-md p-2 font-medium text-sm transition-colors hover:bg-accent',
              pathname === '/help' ? 'text-primary' : 'text-muted-foreground'
            )}
            href="/help"
          >
            <HelpCircle aria-hidden="true" className="mr-2 h-4 w-4" />
            {t('help')}
          </Link>
        </nav>

        {/* Right Side Actions */}
        <div className="flex items-center gap-2 pr-4">
          {/* Theme Toggle */}
          <ThemeToggle />

          {/* Language Selector */}
          <LanguageSelector />

          {/* Session Navigation - Only visible if sessions are active */}
          {hasActiveSessions && !isLoading && (
            <div className="hidden md:block">
              {/* If both sessions are active, show dropdown menu */}
              {adminSession.isActive && censusSession.isActive ? (
                <DropdownMenu
                  onOpenChange={setDropdownOpen}
                  open={dropdownOpen}
                >
                  <DropdownMenuTrigger asChild>
                    <Button
                      className="cursor-pointer gap-1"
                      size="sm"
                      variant="outline"
                    >
                      <span>{tCommon('platform')}</span>
                      <ChevronDown aria-hidden="true" className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem asChild>
                      <Link
                        className="flex cursor-pointer items-center"
                        href={adminSession.returnPath}
                      >
                        <adminSession.icon className="mr-2 h-4 w-4" />
                        <span>{t('adminPortal')}</span>
                      </Link>
                    </DropdownMenuItem>

                    <DropdownMenuItem asChild>
                      <Link
                        className="flex cursor-pointer items-center"
                        href={censusSession.returnPath}
                      >
                        <censusSession.icon className="mr-2 h-4 w-4" />
                        <span>{censusSession.name || tBrand('name')}</span>
                      </Link>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              ) : (
                /* If only one session is active, show direct button */
                <>
                  {adminSession.isActive && (
                    <Button
                      asChild
                      className="cursor-pointer gap-1"
                      size="sm"
                      variant="outline"
                    >
                      <Link href={adminSession.returnPath}>
                        <adminSession.icon className="mr-2 h-4 w-4" />
                        <span>{t('adminPortal')}</span>
                      </Link>
                    </Button>
                  )}

                  {censusSession.isActive && censusSession.returnPath && (
                    <Button
                      asChild
                      className="cursor-pointer gap-1"
                      size="sm"
                      variant="outline"
                    >
                      <Link href={censusSession.returnPath}>
                        <censusSession.icon className="mr-2 h-4 w-4" />
                        <span>{censusSession.name || tBrand('name')}</span>
                      </Link>
                    </Button>
                  )}
                </>
              )}
            </div>
          )}

          {/* Mobile Menu Button */}
          <Button
            aria-expanded={mobileMenuOpen}
            aria-label={tCommon('toggleMobileMenu')}
            className="cursor-pointer md:hidden"
            onClick={toggleMobileMenu}
            size="icon"
            variant="ghost"
          >
            {mobileMenuOpen ? (
              <X aria-hidden="true" className="h-5 w-5" />
            ) : (
              <Menu aria-hidden="true" className="h-5 w-5" />
            )}
            <span className="sr-only">{tCommon('actions')}</span>
          </Button>
        </div>
      </div>

      {/* Mobile Navigation - Overlay Style */}
      {mobileMenuOpen && (
        <>
          {/* Backdrop */}
          <div className="fade-in-0 fixed inset-0 top-16 z-40 animate-in bg-background/80 backdrop-blur-sm duration-100" />

          {/* Menu */}
          <div className="slide-in-from-top-5 fixed inset-x-0 top-16 z-50 animate-in border-t bg-background/95 shadow-lg backdrop-blur-sm duration-200 md:hidden">
            <nav
              aria-label="Mobile navigation"
              className="flex flex-col space-y-4 p-4"
            >
              <Link
                className={cn(
                  'flex cursor-pointer items-center gap-2 rounded-md p-2 font-medium text-sm transition-colors hover:bg-accent',
                  pathname === '/'
                    ? 'bg-muted/50 text-primary'
                    : 'text-foreground'
                )}
                href="/"
                onClick={() => setMobileMenuOpen(false)}
              >
                <Home aria-hidden="true" className="h-4 w-4" />
                {t('home')}
              </Link>
              <Link
                className={cn(
                  'flex cursor-pointer items-center gap-2 rounded-md p-2 font-medium text-sm transition-colors hover:bg-accent',
                  pathname === '/help'
                    ? 'bg-muted/50 text-primary'
                    : 'text-foreground'
                )}
                href="/help"
                onClick={() => setMobileMenuOpen(false)}
              >
                <HelpCircle aria-hidden="true" className="h-4 w-4" />
                {t('help')}
              </Link>

              {/* Session Navigation Links - Only visible if sessions are active */}
              {hasActiveSessions && !isLoading && (
                <>
                  <DropdownMenuSeparator className="my-1" />
                  {adminSession.isActive && censusSession.isActive && (
                    <div className="px-2 font-medium text-muted-foreground text-sm">
                      {tCommon('platform')}
                    </div>
                  )}

                  {adminSession.isActive && (
                    <Link
                      className="flex cursor-pointer items-center gap-2 rounded-md p-2 font-medium text-sm transition-colors hover:bg-accent"
                      href={adminSession.returnPath}
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      <adminSession.icon
                        aria-hidden="true"
                        className="h-4 w-4"
                      />
                      {t('adminPortal')}
                    </Link>
                  )}

                  {censusSession.isActive && censusSession.returnPath && (
                    <Link
                      className="flex cursor-pointer items-center gap-2 rounded-md p-2 font-medium text-sm transition-colors hover:bg-accent"
                      href={censusSession.returnPath}
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      <censusSession.icon
                        aria-hidden="true"
                        className="h-4 w-4"
                      />
                      {censusSession.name || tBrand('name')}
                    </Link>
                  )}
                </>
              )}
            </nav>
          </div>
        </>
      )}
    </header>
  );
}
