/**
 * Database connection module - Prisma Edition
 * Modern PostgreSQL connection using Prisma ORM
 * Replaces MySQL2 connection pooling with Prisma's built-in connection management
 */

// Re-export everything from the Prisma connection layer
// Re-export Prisma client as default for backward compatibility
export {
  checkDatabaseConnection,
  default,
  disconnectDatabase,
  prisma,
  type QueryResult,
  withTransaction,
} from './prisma';
