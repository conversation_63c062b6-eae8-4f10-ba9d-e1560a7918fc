'use client';

import { eachDayOfInterval, getDay } from 'date-fns';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import * as React from 'react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import {
  addMonths,
  endOfMonth,
  formatDate,
  getTodayInSydney,
  isSameDay,
  isSameMonthDate,
  isToday,
  startOfMonth,
  subMonths,
} from '@/lib/utils/date-time';

export interface CalendarProps {
  date: Date | null;
  onDateChange: (date: Date) => void;
  className?: string;
  showFooter?: boolean; // Whether to show the footer with buttons
  onDone?: () => void; // Optional callback for Done button
  onViewChange?: (view: 'day' | 'month' | 'year') => void; // Callback for view changes
  onToday?: () => void; // Callback for Today button click
  view?: 'day' | 'month' | 'year'; // Optional prop to control the view from outside
  onMonthChange?: (month: Date) => void; // Callback for month changes
  showTodayButton?: boolean; // Whether to show the Today button
  standalone?: boolean; // Whether the calendar is used standalone
  maxDate?: Date; // Maximum selectable date (dates after this will be disabled)
  preventFutureDates?: boolean; // Convenience prop to prevent future dates (uses Sydney timezone)
}

export function Calendar({
  date,
  onDateChange,
  className,
  showFooter = false, // Default to false to avoid duplicate footers
  onDone,
  onViewChange,
  onToday,
  view: externalView,
  onMonthChange,
  showTodayButton = true, // Default to true to always show the Today button
  standalone = true, // Default to true for backward compatibility
  maxDate,
  preventFutureDates = false,
}: CalendarProps) {
  // Calculate effective maximum date
  const effectiveMaxDate = React.useMemo(() => {
    if (preventFutureDates) {
      return getTodayInSydney();
    }
    return maxDate;
  }, [maxDate, preventFutureDates]);

  // Function to check if a date is disabled
  const isDateDisabled = React.useCallback(
    (date: Date) => {
      if (!effectiveMaxDate) {
        return false;
      }
      return date > effectiveMaxDate;
    },
    [effectiveMaxDate]
  );

  // State for the calendar
  const [currentMonth, setCurrentMonth] = React.useState(() => new Date());

  // Wrapper for setCurrentMonth that also calls onMonthChange
  const updateCurrentMonth = React.useCallback(
    (month: Date) => {
      setCurrentMonth(month);
      onMonthChange?.(month);
    },
    [onMonthChange]
  );

  // Update currentMonth when date changes (handles both mount and updates)
  React.useEffect(() => {
    if (date) {
      updateCurrentMonth(date);
    }
  }, [date, updateCurrentMonth]);

  // State to track the current view (day, month, year)
  type CalendarView = 'day' | 'month' | 'year';
  const [internalView, setInternalView] = React.useState<CalendarView>('day');

  // Update internalView when externalView changes
  React.useEffect(() => {
    if (externalView) {
      setInternalView(externalView);
    }
  }, [externalView]);

  // Use external view if provided, otherwise use internal view
  const view = externalView || internalView;

  // Generate years array (current year ± 100 years)
  const currentYear = new Date().getFullYear();
  const years = React.useMemo(() => {
    return Array.from({ length: 201 }, (_, i) => currentYear - 100 + i);
  }, [currentYear]);

  // Generate months array
  const months = React.useMemo(() => {
    return [
      { value: 0, label: 'January', short: 'Jan' },
      { value: 1, label: 'February', short: 'Feb' },
      { value: 2, label: 'March', short: 'Mar' },
      { value: 3, label: 'April', short: 'Apr' },
      { value: 4, label: 'May', short: 'May' },
      { value: 5, label: 'June', short: 'Jun' },
      { value: 6, label: 'July', short: 'Jul' },
      { value: 7, label: 'August', short: 'Aug' },
      { value: 8, label: 'September', short: 'Sep' },
      { value: 9, label: 'October', short: 'Oct' },
      { value: 10, label: 'November', short: 'Nov' },
      { value: 11, label: 'December', short: 'Dec' },
    ];
  }, []);

  // Navigation functions
  const nextMonth = () => {
    // Navigate to the next month
    const nextMonthDate = addMonths(currentMonth, 1);
    updateCurrentMonth(nextMonthDate);

    // This ensures the "Today" button visibility is updated correctly
    // when navigating away from the current month
  };

  const prevMonth = () => {
    // Navigate to the previous month
    const prevMonthDate = subMonths(currentMonth, 1);
    updateCurrentMonth(prevMonthDate);

    // This ensures the "Today" button visibility is updated correctly
    // when navigating away from the current month
  };

  // Handle view switching
  const handleViewChange = (newView: 'day' | 'month' | 'year') => {
    // Always update internal view
    setInternalView(newView);

    // Notify parent component if callback is provided
    onViewChange?.(newView);

    // If switching to year view, scroll to center the current year in the viewport
    if (newView === 'year') {
      // Use a small timeout to ensure the DOM is rendered
      setTimeout(() => {
        const yearElement = document.querySelector(
          `[data-year="${currentMonth.getFullYear()}"]`
        );
        if (yearElement) {
          // Use scrollIntoView with center positioning - this was working correctly before
          yearElement.scrollIntoView({ block: 'center', behavior: 'auto' });
        }
      }, 100); // Increased timeout to ensure DOM is fully rendered
    }
  };

  // Handle year selection
  const handleYearSelect = (year: number) => {
    // Update the date with the selected year
    const newDate = new Date(currentMonth);
    newDate.setFullYear(year);
    updateCurrentMonth(newDate);

    // Always switch back to day view after selecting a year
    setInternalView('day'); // Update internal view
    onViewChange?.('day'); // Notify parent component of view change
  };

  // Handle month selection
  const handleMonthSelect = (month: number) => {
    // Update the date with the selected month
    const newDate = new Date(currentMonth);
    newDate.setMonth(month);
    updateCurrentMonth(newDate);

    // Always switch back to day view after selecting a month
    setInternalView('day'); // Update internal view
    onViewChange?.('day'); // Notify parent component of view change
  };

  // Check if the current month being displayed is the current month
  const isCurrentMonthToday = () => {
    const today = new Date();
    return (
      currentMonth.getMonth() === today.getMonth() &&
      currentMonth.getFullYear() === today.getFullYear()
    );
  };

  // Handle "Today" button click
  const handleTodayClick = () => {
    // Navigate to today's date
    const today = new Date();
    updateCurrentMonth(today); // Update the current month to today

    // Update the selected date to today
    onDateChange(today); // Call onDateChange with today's date

    // Always switch back to day view
    setInternalView('day'); // Update internal view
    onViewChange?.('day'); // Notify parent component of view change

    // Call the onToday callback if provided
    onToday?.();
  };

  // Function to handle date selection
  const handleDateSelect = (day: Date) => {
    // Don't allow selection of disabled dates
    if (isDateDisabled(day)) {
      return;
    }

    // Update the selected date
    onDateChange(day);

    // Also update the current month to ensure the calendar displays the correct month
    // This is important for the "Today" button visibility logic
    const newMonth = new Date(day);
    updateCurrentMonth(newMonth);
  };

  // Generate calendar days
  const monthStart = startOfMonth(currentMonth);
  const monthEnd = endOfMonth(currentMonth);
  const days = eachDayOfInterval({ start: monthStart, end: monthEnd });

  // Get day names
  const dayNames = ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'];

  // Calculate the days from the previous month to fill the first row
  const startDay = getDay(monthStart);
  const prevMonthDays =
    startDay > 0
      ? eachDayOfInterval({
          start: new Date(
            monthStart.getFullYear(),
            monthStart.getMonth(),
            -startDay + 1
          ),
          end: new Date(monthStart.getFullYear(), monthStart.getMonth(), 0),
        })
      : [];

  // Calculate the days from the next month to fill the last row
  const endDay = getDay(monthEnd);
  const nextMonthDays =
    endDay < 6
      ? eachDayOfInterval({
          start: new Date(monthEnd.getFullYear(), monthEnd.getMonth() + 1, 1),
          end: new Date(
            monthEnd.getFullYear(),
            monthEnd.getMonth() + 1,
            6 - endDay
          ),
        })
      : [];

  // Combine all days
  const allDays = [...prevMonthDays, ...days, ...nextMonthDays];

  // Create weeks array
  const weeks: Date[][] = [];
  let week: Date[] = [];

  allDays.forEach((day, i) => {
    if (i % 7 === 0 && i > 0) {
      weeks.push(week);
      week = [];
    }
    week.push(day);
    if (i === allDays.length - 1) {
      weeks.push(week);
    }
  });

  return (
    <div className={cn('p-3', className)}>
      {/* Calendar header with interactive month/year */}
      <div className="mb-4 flex items-center justify-between">
        {view === 'day' && (
          <Button
            className="h-7 w-7 cursor-pointer"
            onClick={prevMonth}
            size="icon"
            variant="ghost"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
        )}
        {view !== 'day' && <div className="w-7" />}{' '}
        {/* Spacer when arrows aren't shown */}
        <div className="flex items-center gap-1">
          <button
            className="cursor-pointer rounded-md border border-muted-foreground/20 px-2 py-1 font-medium text-sm hover:border-primary hover:bg-muted"
            onClick={() => handleViewChange('month')}
            type="button"
          >
            {formatDate(currentMonth, 'MMMM')}
          </button>
          <button
            className="cursor-pointer rounded-md border border-muted-foreground/20 px-2 py-1 font-medium text-sm hover:border-primary hover:bg-muted"
            onClick={() => handleViewChange('year')}
            type="button"
          >
            {formatDate(currentMonth, 'yyyy')}
          </button>
        </div>
        {view === 'day' && (
          <Button
            className="h-7 w-7 cursor-pointer"
            onClick={nextMonth}
            size="icon"
            variant="ghost"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        )}
        {view !== 'day' && <div className="w-7" />}{' '}
        {/* Spacer when arrows aren't shown */}
      </div>

      {/* Calendar content based on current view */}
      {view === 'day' && (
        <>
          {/* Day names */}
          <div className="mb-1 grid grid-cols-7">
            {dayNames.map((day) => (
              <div
                className="text-center text-muted-foreground text-sm"
                key={day}
              >
                {day}
              </div>
            ))}
          </div>

          {/* Calendar grid */}
          <div className="space-y-1">
            {weeks.map((week, weekIndex) => (
              <div className="grid grid-cols-7 gap-1" key={weekIndex}>
                {week.map((day, dayIndex) => {
                  const isCurrentMonth = isSameMonthDate(day, currentMonth);
                  const isSelected = date ? isSameDay(day, date) : false;
                  const isTodayDate = isToday(day);
                  const isDisabled = isDateDisabled(day);

                  return (
                    <Button
                      className={cn(
                        'h-8 w-8 p-0 font-normal',
                        !isDisabled && 'cursor-pointer',
                        !isCurrentMonth && 'text-muted-foreground opacity-50',
                        isSelected &&
                          'bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground',
                        isTodayDate &&
                          !isSelected &&
                          'border border-primary text-primary',
                        isDisabled &&
                          'cursor-not-allowed text-muted-foreground opacity-30'
                      )}
                      disabled={isDisabled}
                      key={`${weekIndex}-${dayIndex}`}
                      onClick={() => handleDateSelect(day)}
                      size="icon"
                      variant={isSelected ? 'default' : 'ghost'}
                    >
                      {formatDate(day, 'd')}
                    </Button>
                  );
                })}
              </div>
            ))}
          </div>
        </>
      )}

      {/* Month selection view */}
      {view === 'month' && (
        <div className="grid grid-cols-3 gap-2">
          {months.map((month) => {
            const isSelected = month.value === currentMonth.getMonth();

            return (
              <Button
                className={cn(
                  'h-10 cursor-pointer font-normal',
                  isSelected &&
                    'bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground'
                )}
                key={month.value}
                onClick={() => handleMonthSelect(month.value)}
                variant={isSelected ? 'default' : 'ghost'}
              >
                {month.short}
              </Button>
            );
          })}
        </div>
      )}

      {/* Year selection view */}
      {view === 'year' && (
        <div
          className="h-[220px] overflow-y-auto pr-2"
          onWheel={(e) => {
            // Ensure wheel events are not prevented by parent elements
            e.stopPropagation();
          }}
          style={{
            // Ensure mouse wheel events are properly handled
            overflowY: 'auto',
            // Force hardware acceleration for smoother scrolling
            willChange: 'scroll-position',
          }}
        >
          <div className="grid grid-cols-3 gap-2">
            {years.map((year) => {
              const isSelected = year === currentMonth.getFullYear();

              return (
                <Button
                  className={cn(
                    'h-10 cursor-pointer font-normal',
                    isSelected &&
                      'bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground'
                  )}
                  data-year={year}
                  key={year}
                  onClick={() => handleYearSelect(year)}
                  onMouseDown={(e) => {
                    // Prevent button from capturing mouse events that should go to scroll
                    if (e.button === 1) {
                      // Middle mouse button
                      e.preventDefault();
                    }
                  }}
                  variant={isSelected ? 'default' : 'ghost'}
                >
                  {year}
                </Button>
              );
            })}
          </div>
        </div>
      )}

      {/* Footer with Today and Done buttons - shown when showFooter is true OR when standalone is true */}
      {(showFooter || standalone) && (
        <div className="mt-2 flex justify-between border-t p-3">
          {/* Show Today button in month/year views OR when not viewing current month in day view */}
          {showTodayButton &&
          (view === 'month' || view === 'year' || !isCurrentMonthToday()) ? (
            <Button
              className="cursor-pointer"
              onClick={handleTodayClick}
              size="sm"
              variant="ghost"
            >
              Today
            </Button>
          ) : (
            <div /> /* Empty div to maintain layout when Today button is not shown */
          )}
          {/* Done button - shown when showFooter is true OR when standalone is true */}
          {showFooter || standalone ? (
            <Button
              className="cursor-pointer"
              onClick={() => onDone?.()}
              size="sm"
            >
              Done
            </Button>
          ) : (
            <div /> /* Empty div to maintain layout when Done button is not shown */
          )}
        </div>
      )}
    </div>
  );
}
