'use client';

import { useEffect } from 'react';
import { useAlert } from '@/contexts/AlertContext';
import { useToastTranslation } from '@/hooks/useToastTranslation';

type ToastHandlerProps = {
  toast: {
    type: 'success' | 'error' | 'info' | 'warning';
    message: string;
  } | null;
};

export function HomeToastHandler({ toast }: ToastHandlerProps) {
  const { showAlert } = useAlert();
  const { enhanceToastMessage } = useToastTranslation();

  useEffect(() => {
    if (toast) {
      const enhancedMessage = enhanceToastMessage(toast.message, 'census');

      setTimeout(() => {
        showAlert(toast.type, enhancedMessage);
      }, 500);
    }
  }, [toast, showAlert, enhanceToastMessage]);

  return null;
}
