/**
 * Keyword Query Executor Module
 *
 * Contains the main keyword query execution logic that processes user messages
 * and routes them to appropriate query handlers based on keyword matching.
 *
 * This is the core fallback system when intent-based analysis doesn't provide
 * sufficient confidence for query processing.
 */

import type { NextRequest } from 'next/server';
import { logSecureError } from '@/lib/analytics/chatbot/security';
import { prisma } from '@/lib/db/prisma';
import {
  handleAgeBasedQueries,
  handleCensusFormQueries,
  handleCensusYearQueries,
  handleLocationQueries,
  handleRelationshipQueries,
  handleSacramentQueries,
} from './keyword-queries';

// Main query execution function with comprehensive keyword matching
export async function executeQueriesWithTimeout(
  message: string,
  results: string[],
  _request: NextRequest
): Promise<string[]> {
  // SECURITY: Only access explicitly allowed tables through Prisma models
  // This function only uses Prisma models which inherently restrict table access

  try {
    // DEBUG: Log message analysis
    if (process.env.NODE_ENV === 'development') {
      console.log(
        'Keyword query analysis for message:',
        message.substring(0, 100)
      );
    }

    // GENDER DISTRIBUTION QUERIES: Handle specifically for consistency
    if (
      message.includes('gender') &&
      (message.includes('chart') ||
        message.includes('distribution') ||
        message.includes('count'))
    ) {
      const memberCount = await prisma.member.count();
      results.push(`Total members: ${memberCount}`);

      if (memberCount > 0) {
        // 2025 Performance: Use optimized PostgreSQL query
        const genderStats = await prisma.$queryRaw<
          Array<{ gender: string; count: bigint }>
        >`
          SELECT
            COALESCE(gender, 'Unknown') as gender,
            COUNT(*) as count
          FROM members
          GROUP BY gender
          ORDER BY count DESC
        `;

        const genderTotal = genderStats.reduce(
          (sum, stat) => sum + Number(stat.count),
          0
        );
        const genderSummary = genderStats
          .map((stat) => `${stat.gender}: ${Number(stat.count)}`)
          .join(', ');
        results.push(`Gender distribution: ${genderSummary}`);

        // Always include chart for gender queries
        if (genderStats.length > 0) {
          const chartData = {
            type: 'pie',
            title: 'Member Gender Distribution',
            data: genderStats.map((stat) => ({
              name: stat.gender,
              value: Number(stat.count),
            })),
          };
          results.push(`CHART_DATA: ${JSON.stringify(chartData)}`);
        }

        // Debug consistency
        if (
          process.env.NODE_ENV === 'development' &&
          genderTotal !== memberCount
        ) {
        }
      } else {
        results.push('No members found in the database.');
      }

      return results; // Return early for gender-specific queries
    }

    // AGE-BASED QUERIES: Handle birth year and age filtering specifically
    if (
      (message.includes('born') ||
        message.includes('birth') ||
        message.includes('age')) &&
      (message.includes('after') ||
        message.includes('before') ||
        message.includes('in') ||
        message.includes('since') ||
        message.includes('during') ||
        /\b(19|20)\d{2}\b/.test(message))
    ) {
      if (process.env.NODE_ENV === 'development') {
        console.log('Handling age-based query');
      }
      await handleAgeBasedQueries(message, results);
      return results;
    }

    // MEMBER-RELATED QUERIES: Handle comprehensive member queries
    if (
      message.includes('member') ||
      message.includes('people') ||
      message.includes('person')
    ) {
      // CONSISTENCY FIX: Use the same filtering logic as semantic queries
      // This ensures both query paths return consistent results

      // For keyword queries, we should count ALL members (no filtering)
      // to match the user's expectation of "total members"
      const memberCount = await prisma.member.count();
      results.push(`Total members: ${memberCount}`);

      if (memberCount > 0) {
        // CONSISTENCY FIX: Use the same no-filter approach for gender distribution
        // This ensures the gender distribution matches the total count
        // 2025 Performance: Use optimized PostgreSQL query for consistency
        const genderStats = await prisma.$queryRaw<
          Array<{ gender: string; count: bigint }>
        >`
          SELECT
            COALESCE(gender, 'Unknown') as gender,
            COUNT(*) as count
          FROM members
          GROUP BY gender
          ORDER BY count DESC
        `;

        // Verify the counts match
        const genderTotal = genderStats.reduce(
          (sum, stat) => sum + Number(stat.count),
          0
        );
        const genderSummary = genderStats
          .map((stat) => `${stat.gender}: ${Number(stat.count)}`)
          .join(', ');
        results.push(`Gender distribution: ${genderSummary}`);

        // Add debug info in development to help identify discrepancies
        if (
          process.env.NODE_ENV === 'development' &&
          genderTotal !== memberCount
        ) {
        }

        // CHART GENERATION: Add chart for gender distribution if requested
        if (
          (message.includes('chart') ||
            message.includes('graph') ||
            message.includes('visual')) &&
          genderStats.length > 0
        ) {
          const chartData = {
            type: 'pie',
            title: 'Member Gender Distribution',
            data: genderStats.map((stat) => ({
              name: stat.gender,
              value: Number(stat.count),
            })),
          };
          results.push(`CHART_DATA: ${JSON.stringify(chartData)}`);
        }

        // If user is asking for specific member details
        if (
          message.includes('list') ||
          message.includes('show me') ||
          message.includes('give me') ||
          message.includes('names')
        ) {
          const members = await prisma.member.findMany({
            take: 5, // Limit to first 5 members
            select: {
              firstName: true,
              lastName: true,
              gender: true,
            },
          });

          if (members.length > 0) {
            const memberList = members
              .map((m) => `${m.firstName} ${m.lastName} (${m.gender})`)
              .join(', ');
            results.push(`Sample members: ${memberList}`);
          }
        }

        // Note: Age-based queries are handled at the top level now
      }

      return results; // Return early for member-specific queries
    }

    // HOUSEHOLD-RELATED QUERIES: Handle comprehensive household queries
    if (
      message.includes('household') ||
      message.includes('family') ||
      message.includes('families')
    ) {
      const householdCount = await prisma.household.count();
      results.push(`Total households: ${householdCount}`);

      if (householdCount > 0) {
        // 2025 Performance: Use optimized PostgreSQL query for suburb distribution
        const suburbStats = await prisma.$queryRaw<
          Array<{ suburb: string; count: bigint }>
        >`
          SELECT
            suburb,
            COUNT(*) as count
          FROM households
          GROUP BY suburb
          ORDER BY count DESC
          LIMIT 5
        `;
        const suburbSummary = suburbStats
          .map((stat) => `${stat.suburb}: ${Number(stat.count)}`)
          .join(', ');
        results.push(`Top suburbs: ${suburbSummary}`);
      }

      return results; // Return early for household-specific queries
    }

    // UNIQUE CODES QUERIES: Handle unique code management
    if (message.includes('unique') || message.includes('code')) {
      const codeCount = await prisma.uniqueCode.count();
      results.push(`Total unique codes: ${codeCount}`);

      if (codeCount > 0) {
        const usedCodes = await prisma.uniqueCode.count({
          where: { isAssigned: true },
        });
        const availableCodes = codeCount - usedCodes;
        results.push(
          `Used codes: ${usedCodes}, Available codes: ${availableCodes}`
        );

        // If user is asking for a specific code (contains "give me" or "get me" or "show me")
        if (
          message.includes('give me') ||
          message.includes('get me') ||
          message.includes('show me') ||
          message.includes('fetch')
        ) {
          const availableCode = await prisma.uniqueCode.findFirst({
            where: { isAssigned: false },
            select: { code: true },
          });

          if (availableCode) {
            results.push(`Available unique code: ${availableCode.code}`);
          } else {
            results.push('No available unique codes found');
          }
        }
      }

      return results; // Return early for unique code queries
    }

    // SACRAMENT QUERIES: Handle sacrament-related questions
    if (
      message.includes('sacrament') ||
      message.includes('baptism') ||
      message.includes('communion') ||
      message.includes('confirmation') ||
      message.includes('marriage') ||
      message.includes('ordination')
    ) {
      if (process.env.NODE_ENV === 'development') {
        console.log('Handling sacrament query');
      }
      await handleSacramentQueries(message, results);
      return results;
    }

    // CENSUS YEAR QUERIES: Handle census year information
    if (
      message.includes('census year') ||
      message.includes('year') ||
      (message.includes('census') && /\b20\d{2}\b/.test(message))
    ) {
      if (process.env.NODE_ENV === 'development') {
        console.log('Handling census year query');
      }
      await handleCensusYearQueries(message, results);
      return results;
    }

    // RELATIONSHIP QUERIES: Handle household relationship questions
    if (
      message.includes('relationship') ||
      message.includes('head') ||
      message.includes('spouse') ||
      message.includes('child') ||
      message.includes('parent') ||
      message.includes('family')
    ) {
      if (process.env.NODE_ENV === 'development') {
        console.log('Handling relationship query');
      }
      await handleRelationshipQueries(message, results);
      return results;
    }

    // LOCATION QUERIES: Handle suburb and location questions
    if (
      message.includes('suburb') ||
      message.includes('location') ||
      message.includes('address') ||
      message.includes('where') ||
      message.includes('live')
    ) {
      if (process.env.NODE_ENV === 'development') {
        console.log('Handling location query');
      }
      await handleLocationQueries(message, results);
      return results;
    }

    // CENSUS FORM QUERIES: Handle form status and feedback
    if (
      message.includes('form') ||
      message.includes('status') ||
      message.includes('feedback') ||
      message.includes('comment') ||
      message.includes('complete')
    ) {
      if (process.env.NODE_ENV === 'development') {
        console.log('Handling census form query');
      }
      await handleCensusFormQueries(message, results);
      return results;
    }

    // GENERAL STATISTICS QUERIES: Handle overview/summary/total requests
    if (
      message.includes('overview') ||
      message.includes('summary') ||
      message.includes('total') ||
      message.includes('how many')
    ) {
      if (process.env.NODE_ENV === 'development') {
        console.log('Handling general statistics query');
      }

      const [
        memberCount,
        householdCount,
        codeCount,
        sacramentCount,
        censusYearCount,
      ] = await Promise.all([
        prisma.member.count(),
        prisma.household.count(),
        prisma.uniqueCode.count(),
        prisma.sacrament.count(),
        prisma.censusYear.count(),
      ]);

      results.push(
        `Database overview: ${memberCount} members, ${householdCount} households, ${codeCount} unique codes, ${sacramentCount} sacrament records, ${censusYearCount} census years`
      );

      return results;
    }

    // MEMBER COUNT QUERIES: Simple member counting (fallback for specific counts)
    if (message.includes('count') || message.includes('number')) {
      if (process.env.NODE_ENV === 'development') {
        console.log('Handling count query');
      }

      const [memberCount, householdCount, codeCount] = await Promise.all([
        prisma.member.count(),
        prisma.household.count(),
        prisma.uniqueCode.count(),
      ]);

      if (message.includes('member')) {
        results.push(`Total members: ${memberCount}`);
      } else if (message.includes('household')) {
        results.push(`Total households: ${householdCount}`);
      } else if (message.includes('code')) {
        results.push(`Total unique codes: ${codeCount}`);
      } else {
        // General count summary
        results.push(
          `Database summary: ${memberCount} members, ${householdCount} households, ${codeCount} unique codes`
        );
      }
      return results;
    }

    // HOBBY QUERIES: Handle hobby-related questions
    if (message.includes('hobby') || message.includes('hobbies')) {
      if (process.env.NODE_ENV === 'development') {
        console.log('Handling hobby query');
      }

      const hobbyStats = await prisma.$queryRaw<
        Array<{ hobby: string; count: bigint }>
      >`
        SELECT
          hobby,
          COUNT(*) as count
        FROM members
        WHERE hobby IS NOT NULL
        GROUP BY hobby
        ORDER BY count DESC
        LIMIT 10
      `;

      if (hobbyStats.length > 0) {
        const hobbySummary = hobbyStats
          .map((stat) => `${stat.hobby}: ${Number(stat.count)}`)
          .join(', ');
        results.push(`Top hobbies: ${hobbySummary}`);
      } else {
        results.push('No hobby data found.');
      }
      return results;
    }

    // OCCUPATION QUERIES: Handle occupation-related questions
    if (
      message.includes('occupation') ||
      message.includes('job') ||
      message.includes('work')
    ) {
      if (process.env.NODE_ENV === 'development') {
        console.log('Handling occupation query');
      }

      const occupationStats = await prisma.$queryRaw<
        Array<{ occupation: string; count: bigint }>
      >`
        SELECT
          occupation,
          COUNT(*) as count
        FROM members
        WHERE occupation IS NOT NULL
        GROUP BY occupation
        ORDER BY count DESC
        LIMIT 10
      `;

      if (occupationStats.length > 0) {
        const occupationSummary = occupationStats
          .map((stat) => `${stat.occupation}: ${Number(stat.count)}`)
          .join(', ');
        results.push(`Top occupations: ${occupationSummary}`);
      } else {
        results.push('No occupation data found.');
      }
      return results;
    }

    // DEFAULT: General database overview
    if (process.env.NODE_ENV === 'development') {
      console.log('Providing general database overview');
    }

    const [memberCount, householdCount, codeCount, sacramentCount] =
      await Promise.all([
        prisma.member.count(),
        prisma.household.count(),
        prisma.uniqueCode.count(),
        prisma.sacrament.count(),
      ]);

    results.push(
      `Database overview: ${memberCount} members, ${householdCount} households, ${codeCount} unique codes, ${sacramentCount} sacrament records`
    );

    // Add helpful suggestions
    if (memberCount === 0) {
      results.push(
        '💡 Tip: Start by adding member data to begin census tracking.'
      );
    } else if (householdCount === 0) {
      results.push(
        '💡 Tip: Consider organizing members into households for better census management.'
      );
    } else if (codeCount === 0) {
      results.push(
        '💡 Tip: Generate unique codes to enable census participation tracking.'
      );
    }
  } catch (error) {
    logSecureError('keyword_query_execution', error, {
      message: message.substring(0, 100),
    });
    results.push('Unable to process query at this time.');
  }

  return results;
}
