import type { NextAuthOptions } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import { generateCensusRateLimitToken } from '@/lib/auth/census-rate-limit-utils';
import {
  isSessionLocked,
  recordFailedAttempt,
  resetRateLimit,
} from '@/lib/auth/rate-limiting-service';
import { isCensusOpen } from '@/lib/census/census-availability';
import { prisma } from '@/lib/db/prisma';
import { validateUniqueCode } from '@/lib/db/unique-codes';

export const censusAuthOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'CensusCode',
      credentials: {
        code: { label: 'Unique Code', type: 'text' },
      },
      async authorize(credentials, req: { headers?: { cookie?: string } }) {
        if (!credentials?.code) {
          return null;
        }
        // Rate limiting for census authentication attempts
        // Always use IP-based rate limiting for login attempts (more secure and logical)
        const headers = req?.headers as
          | Record<string, string | string[] | undefined>
          | undefined;
        const sessionToken = generateCensusRateLimitToken(headers);

        // Check if session is currently locked out
        if (await isSessionLocked(sessionToken)) {
          const error = new Error('TooManyAttempts');
          error.name = 'TooManyAttempts';
          throw error;
        }

        try {
          // Check if census is open
          const censusStatus = await isCensusOpen();
          if (!censusStatus.isOpen) {
            const error = new Error('CensusClosed');
            error.name = 'CensusClosed';
            throw error;
          }

          // Validate the unique code
          // This will now accept codes from any census year, not just the active one
          // The only restriction is whether the census system is open or closed
          const uniqueCode = await validateUniqueCode(credentials.code);

          if (!uniqueCode) {
            // Record failed attempt for rate limiting
            const rateLimitResult = await recordFailedAttempt(sessionToken);

            // Check if this attempt triggered a lockout
            if (!rateLimitResult.allowed) {
              const error = new Error('TooManyAttempts');
              error.name = 'TooManyAttempts';
              throw error;
            }

            return null;
          }

          // Successful authentication - reset rate limiting
          await resetRateLimit(sessionToken);

          // Mark the code as assigned if it's not already
          // Note: We still allow login with assigned codes - this just tracks first usage
          if (!uniqueCode.isAssigned) {
            await prisma.uniqueCode.update({
              where: { id: uniqueCode.id },
              data: {
                isAssigned: true,
                assignedAt: new Date(),
              },
            });
          }

          // For assigned codes, update the last_activity timestamp to track usage
          // This is useful for analytics but doesn't affect authentication
          if (uniqueCode.isAssigned) {
            try {
              await prisma.uniqueCode.update({
                where: { id: uniqueCode.id },
                data: { updatedAt: new Date() },
              });
            } catch (error) {
              // Don't fail authentication if this update fails
              console.error('Failed to update last activity for code:', error);
            }
          }

          // Get household information if available
          const householdId = uniqueCode.householdId;
          let householdName = 'Household';

          if (householdId) {
            // Get the household head's last name
            const householdHead = await prisma.householdMember.findFirst({
              where: {
                householdId,
                relationship: 'head',
              },
              include: {
                member: {
                  select: { lastName: true },
                },
              },
            });

            if (householdHead?.member) {
              householdName = householdHead.member.lastName;
            } else {
              // Fallback to suburb if no household head is found
              const household = await prisma.household.findUnique({
                where: { id: householdId },
                select: { suburb: true },
              });

              if (household?.suburb) {
                householdName = household.suburb;
              }
            }
          }

          // Return the user object
          return {
            id: uniqueCode.id.toString(),
            code: uniqueCode.code,
            role: 'household',
            name: householdName,
            householdId: householdId ? householdId.toString() : null,
            censusYearId: uniqueCode.censusYearId.toString(),
          };
        } catch (error) {
          // Record failed attempt for any authentication error
          if (
            error instanceof Error &&
            error.name !== 'CensusClosed' &&
            error.name !== 'TooManyAttempts'
          ) {
            const rateLimitResult = await recordFailedAttempt(sessionToken);

            // Check if this attempt triggered a lockout
            if (!rateLimitResult.allowed) {
              const lockoutError = new Error('TooManyAttempts');
              lockoutError.name = 'TooManyAttempts';
              throw lockoutError;
            }
          }
          throw error;
        }
      },
    }),
  ],
  pages: {
    signIn: '/', // Redirect to homepage for census login
    error: '/', // Redirect to homepage for errors
  },
  events: {
    async signOut() {
      // Handle sign out events silently for security
    },
  },
  logger: {
    error(code, metadata) {
      // Custom error logging to reduce spam for expected account deletion errors
      if (
        code === 'JWT_SESSION_ERROR' &&
        metadata?.message === 'ACCOUNT_DELETED'
      ) {
        // This is expected behaviour when accounts are deleted - silently handle
        return;
      }
      // Suppress other errors in production for security
    },
    warn() {
      // Suppress warnings in all environments for security
    },
    debug() {
      // Suppress debug logs in all environments for security
    },
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        // Initial sign-in, set the token from the user object
        token.id = user.id;
        token.code = user.code;
        token.role = user.role;
        token.name = user.name;
        token.householdId = user.householdId;
        token.censusYearId = user.censusYearId;
      } else if (token.id && token.role === 'household') {
        try {
          // Only process census tokens (household role)
          // On token refresh, check if the household_id has been updated in the database
          const uniqueCodeId = Number.parseInt(token.id as string, 10);

          // Validate that the ID is a valid number
          if (Number.isNaN(uniqueCodeId)) {
            if (process.env.NODE_ENV === 'development') {
              console.error(
                'Census JWT callback: Invalid unique code ID:',
                token.id
              );
            }
            return token;
          }

          const uniqueCode = await prisma.uniqueCode.findUnique({
            where: { id: uniqueCodeId },
            select: { householdId: true },
          });

          if (uniqueCode) {
            const dbHouseholdId = uniqueCode.householdId;

            // Check if household was deleted (household_id became null)
            if (dbHouseholdId === null && token.householdId !== null) {
              // Household was deleted by admin, mark session for invalidation
              token.accountDeleted = true;
              token.householdId = null;
            } else if (
              dbHouseholdId &&
              (!token.householdId ||
                token.householdId !== dbHouseholdId.toString())
            ) {
              // If the household_id in the database is different from the token, update the token
              token.householdId = dbHouseholdId.toString();
              // Clear any previous account deletion flag
              token.accountDeleted = false;
            }

            // Always check for household head name updates when we have a valid household
            if (dbHouseholdId && token.householdId) {
              const householdHead = await prisma.householdMember.findFirst({
                where: {
                  householdId: dbHouseholdId,
                  relationship: 'head',
                },
                include: {
                  member: {
                    select: { lastName: true },
                  },
                },
              });

              if (householdHead?.member) {
                // Update the token name with the current household head's last name
                token.name = householdHead.member.lastName;
              }
            }
          } else {
            // Unique code doesn't exist, mark for session invalidation
            token.accountDeleted = true;
            token.householdId = null;
          }
        } catch (_error) {
          // Continue with the existing token if there's an error
          // Don't let database errors break the authentication flow
        }
      } else if (token.id && token.role !== 'household') {
        // This is not a census token (probably admin), don't process it
        return token;
      }
      return token;
    },
    async session({ session, token }) {
      if (token && token.role === 'household') {
        // Only process census tokens (household role)
        // Check if account was deleted - throw error to trigger proper logout
        // This is the correct security behaviour for deleted accounts
        if (token.accountDeleted) {
          // Throw an error to trigger NextAuth.js error handling
          // This is more secure than creating an invalid session
          throw new Error('ACCOUNT_DELETED');
        }

        session.user = {
          id: token.id as string,
          code: token.code as string,
          role: token.role as string,
          name: token.name as string,
          householdId: token.householdId as string | null,
          censusYearId: token.censusYearId as string,
        };
      }
      return session;
    },
  },
  session: {
    strategy: 'jwt',
    maxAge: 8 * 60 * 60, // 8 hours, same as admin sessions
  },
  cookies: {
    // Use different cookie names to avoid conflicts with admin auth
    sessionToken: {
      name: 'census-session-token',
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
      },
    },
    callbackUrl: {
      name: 'census-callback-url',
      options: {
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
      },
    },
    csrfToken: {
      name: 'census-csrf-token',
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
      },
    },
  },
  // Use a dedicated secret key for census authentication
  // This ensures complete separation from the admin authentication system
  secret: process.env.NEXTAUTH_SECRET_CENSUS,
};
