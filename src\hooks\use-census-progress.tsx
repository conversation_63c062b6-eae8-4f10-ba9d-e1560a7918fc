'use client';

import { useSession } from 'next-auth/react';
import type React from 'react';
import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

// Step percentage constants - single source of truth for progress calculation
export const STEP_PERCENTAGES = {
  householdRegistration: 15,
  hobbies: 17,
  occupation: 17,
  sacraments: 17,
  familyMembers: 17,
  communityFeedback: 17,
} as const;

// Progress completion state interface - exported for backward compatibility
export interface CompletionState {
  householdRegistration: boolean; // Always true after login (auto-complete)
  hobbies: boolean; // True if household head has hobby filled
  occupation: boolean; // True if household head has occupation filled
  sacraments: boolean; // True if household head has sacraments
  familyMembers: boolean; // True if household has more than just the head
  communityFeedback: boolean; // True if community feedback text is entered
}

// Context interface
interface CensusProgressContextType {
  progress: number; // Progress percentage (0-100)
  completionState: CompletionState;
  updateProgress: (newState: Partial<CompletionState>) => void;
  refreshProgress: () => Promise<void>; // Event-driven refresh - call after data changes
  isLoading: boolean;
}

// Storage key for progress state (hydration only)
const PROGRESS_STORAGE_KEY = 'census-progress-state';

// Create context
const CensusProgressContext = createContext<CensusProgressContextType | null>(
  null
);

/**
 * CensusProgressProvider - Global state management for census progress
 *
 * Features:
 * - Shared state across all components
 * - Real-time synchronisation
 * - Event-driven updates
 * - SSR hydration consistency
 * - Memory leak prevention
 * - Authentication-gated API calls
 */
export function CensusProgressProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const { data: session, status } = useSession();
  const [completionState, setCompletionState] = useState<CompletionState>({
    householdRegistration: true, // Always true after login
    hobbies: false,
    occupation: false,
    sacraments: false,
    familyMembers: false,
    communityFeedback: false,
  });

  const [isLoading, setIsLoading] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  // Refs for cleanup and preventing race conditions
  const mountedRef = useRef(true);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Authentication state derived from session
  const isAuthenticated = Boolean(
    session?.user?.id &&
      session?.user?.householdId &&
      session?.user?.censusYearId &&
      status === 'authenticated'
  );

  // Loading state that respects session initialisation
  const isSessionLoading = status === 'loading';

  // Calculate progress percentage based on completion state
  const calculateProgress = useCallback((state: CompletionState): number => {
    let progress = 0;

    // Use step percentages constant for maintainability
    if (state.householdRegistration) {
      progress += STEP_PERCENTAGES.householdRegistration;
    }
    if (state.hobbies) {
      progress += STEP_PERCENTAGES.hobbies;
    }
    if (state.occupation) {
      progress += STEP_PERCENTAGES.occupation;
    }
    if (state.sacraments) {
      progress += STEP_PERCENTAGES.sacraments;
    }
    if (state.familyMembers) {
      progress += STEP_PERCENTAGES.familyMembers;
    }
    if (state.communityFeedback) {
      progress += STEP_PERCENTAGES.communityFeedback;
    }

    return Math.min(progress, 100); // Ensure progress doesn't exceed 100%
  }, []);

  // Load cached progress state from localStorage
  const loadCachedState = useCallback((): CompletionState | null => {
    if (typeof window === 'undefined') {
      return null;
    }

    try {
      const cached = localStorage.getItem(PROGRESS_STORAGE_KEY);
      if (!cached) {
        return null;
      }

      const parsed = JSON.parse(cached);

      // Basic structure validation
      if (!parsed || typeof parsed !== 'object' || !parsed.state) {
        throw new Error('Invalid cache structure');
      }

      // Validate cached data structure
      const state = parsed.state;
      const requiredFields = [
        'householdRegistration',
        'hobbies',
        'sacraments',
        'familyMembers',
      ];

      for (const field of requiredFields) {
        if (typeof state[field] !== 'boolean') {
          throw new Error(`Invalid cached field: ${field}`);
        }
      }

      return state;
    } catch (_error) {
      // Clear corrupted cache
      try {
        localStorage.removeItem(PROGRESS_STORAGE_KEY);
      } catch {
        // Ignore cleanup errors
      }
      return null;
    }
  }, []);

  // Save progress state to localStorage
  const saveCachedState = useCallback(
    (state: CompletionState) => {
      if (typeof window === 'undefined') {
        return;
      }

      // Security: Only cache when properly authenticated
      if (!session?.user?.id) {
        return;
      }

      try {
        // Security: Include session ID to prevent cross-session cache pollution
        const cacheData = {
          state,
          timestamp: Date.now(),
          sessionId: session.user.id,
          version: '1.0',
        };
        localStorage.setItem(PROGRESS_STORAGE_KEY, JSON.stringify(cacheData));
      } catch (_error) {
        // Silently handle cache save errors
      }
    },
    [session?.user?.id]
  );

  // Fetch progress from API
  const fetchProgressFromAPI =
    useCallback(async (): Promise<CompletionState> => {
      // Security: Comprehensive authentication validation
      if (!isAuthenticated) {
        return {
          householdRegistration: true, // Always true after login
          hobbies: false,
          occupation: false,
          sacraments: false,
          familyMembers: false,
          communityFeedback: false,
        };
      }

      // Performance: Cancel previous request if still pending
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      abortControllerRef.current = new AbortController();

      try {
        const response = await fetch('/api/census/progress', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          signal: abortControllerRef.current.signal,
          cache: 'no-store',
        });

        if (!response.ok) {
          throw new Error(`HTTP_${response.status}`);
        }

        const data = await response.json();

        // Security: Validate response data structure
        if (!data.data || typeof data.data !== 'object') {
          throw new Error('INVALID_RESPONSE_DATA');
        }

        if (
          !data.data.completionState ||
          typeof data.data.completionState !== 'object'
        ) {
          throw new Error('INVALID_COMPLETION_STATE');
        }

        // Memory: Check if component is still mounted before returning
        if (!mountedRef.current) {
          throw new Error('COMPONENT_UNMOUNTED');
        }

        // Security: Validate completion state structure
        const completionState = data.data.completionState;
        const requiredFields = [
          'householdRegistration',
          'hobbies',
          'occupation',
          'sacraments',
          'familyMembers',
          'communityFeedback',
        ];

        for (const field of requiredFields) {
          if (typeof completionState[field] !== 'boolean') {
            throw new Error(
              `INVALID_COMPLETION_STATE_FIELD_${field.toUpperCase()}`
            );
          }
        }

        return completionState;
      } catch (error) {
        // Don't log aborted requests (normal cleanup behaviour)
        if (error instanceof Error && error.name === 'AbortError') {
          throw error;
        }

        // Graceful degradation: Return default state on error
        return {
          householdRegistration: true, // Always true after login
          hobbies: false,
          occupation: false,
          sacraments: false,
          familyMembers: false,
          communityFeedback: false,
        };
      }
    }, [isAuthenticated]);

  // Event-driven progress refresh - fetches fresh data when triggered by user actions
  const refreshProgress = useCallback(async () => {
    // Security: Only refresh when properly authenticated
    if (!isAuthenticated) {
      return;
    }

    // Performance: Check if component is still mounted
    if (!mountedRef.current) {
      return;
    }

    setIsLoading(true);
    try {
      const newState = await fetchProgressFromAPI();

      // Memory: Only update state if component is still mounted
      if (mountedRef.current) {
        setCompletionState(newState);
        saveCachedState(newState);
      }
    } catch (error) {
      // Don't handle aborted requests (normal cleanup)
      if (error instanceof Error && error.name === 'AbortError') {
        return;
      }

      // Graceful degradation: Fall back to cached state if available
      const cached = loadCachedState();
      if (cached && mountedRef.current) {
        setCompletionState(cached);
      }
    } finally {
      // Memory: Only update loading state if component is still mounted
      if (mountedRef.current) {
        setIsLoading(false);
      }
    }
  }, [isAuthenticated, loadCachedState, fetchProgressFromAPI, saveCachedState]);

  // Update progress state with optimistic updates
  const updateProgress = useCallback(
    (newState: Partial<CompletionState>) => {
      setCompletionState((prev) => {
        const updated = { ...prev, ...newState };
        // Optimistic update: Save to cache immediately
        saveCachedState(updated);
        return updated;
      });
    },
    [saveCachedState]
  );

  // Phase 1: Immediate cache loading for SSR hydration consistency
  useEffect(() => {
    // Performance: Check if component is still mounted
    if (!mountedRef.current) {
      return;
    }

    // Hydration Fix: Load cached data immediately for consistent rendering
    const cached = loadCachedState();
    if (cached) {
      setCompletionState(cached);
    }

    // Mark as initialised to prevent loading state flicker
    setIsInitialized(true);
  }, [loadCachedState]);

  // Phase 2: Event-driven initialisation (load once on authentication)
  useEffect(() => {
    // Security: Wait for session to fully load
    if (isSessionLoading) {
      return;
    }

    // Performance: Check if component is still mounted
    if (!mountedRef.current) {
      return;
    }

    // Security: Only proceed when properly authenticated
    if (isAuthenticated) {
      // Load initial progress data once
      refreshProgress();
    }
    // Graceful degradation: If not authenticated, keep existing state
  }, [isSessionLoading, isAuthenticated, refreshProgress]);

  // Memory leak prevention: Cleanup on unmount
  useEffect(() => {
    return () => {
      mountedRef.current = false;
      // Cancel any pending requests
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  const progress = calculateProgress(completionState);

  // Memoize context value to prevent unnecessary re-renders
  const contextValue: CensusProgressContextType = useMemo(
    () => ({
      progress,
      completionState,
      updateProgress,
      refreshProgress,
      isLoading: isLoading || !isInitialized,
    }),
    [
      progress,
      completionState,
      updateProgress,
      refreshProgress,
      isLoading,
      isInitialized,
    ]
  );

  return (
    <CensusProgressContext.Provider value={contextValue}>
      {children}
    </CensusProgressContext.Provider>
  );
}

/**
 * Hook to consume census progress context
 * Throws error if used outside of CensusProgressProvider
 */
export function useCensusProgress(): CensusProgressContextType {
  const context = useContext(CensusProgressContext);

  if (!context) {
    throw new Error(
      'useCensusProgress must be used within a CensusProgressProvider'
    );
  }

  return context;
}
