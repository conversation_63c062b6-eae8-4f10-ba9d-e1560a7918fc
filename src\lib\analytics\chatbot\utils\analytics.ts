/**
 * Analytics Module
 *
 * Contains analytics tracking and performance monitoring for the chatbot system.
 * This module handles tracking of user interactions, performance metrics, and
 * system health monitoring.
 *
 * This module provides:
 * - Request tracking and performance monitoring
 * - Error tracking and analytics
 * - Cache hit rate monitoring
 * - Response time measurement
 * - Development debugging and statistics
 */

// Analytics event types
export type AnalyticsEvent = 'request' | 'cache_hit' | 'error';

// Analytics metrics interface
export interface AnalyticsMetrics {
  totalRequests: number;
  cacheHits: number;
  errorCount: number;
  avgResponseTime: number;
  lastReset: number;
}

// Analytics tracker class for better encapsulation
class ChatbotAnalyticsTracker {
  private metrics: AnalyticsMetrics = {
    totalRequests: 0,
    cacheHits: 0,
    errorCount: 0,
    avgResponseTime: 0,
    lastReset: Date.now(),
  };

  /**
   * Track an analytics event with optional response time
   */
  track(event: AnalyticsEvent, responseTime?: number): void {
    switch (event) {
      case 'request':
        this.metrics.totalRequests++;
        if (responseTime) {
          // Calculate rolling average for response time
          this.metrics.avgResponseTime =
            (this.metrics.avgResponseTime + responseTime) / 2;
        }
        break;
      case 'cache_hit':
        this.metrics.cacheHits++;
        break;
      case 'error':
        this.metrics.errorCount++;
        break;
    }

    // Log stats every 100 requests in development
    if (
      process.env.NODE_ENV === 'development' &&
      this.metrics.totalRequests % 100 === 0
    ) {
      this.logDevelopmentStats();
    }
  }

  /**
   * Get current analytics metrics
   */
  getMetrics(): Readonly<AnalyticsMetrics> {
    return { ...this.metrics };
  }

  /**
   * Get cache hit rate as a percentage
   */
  getCacheHitRate(): number {
    if (this.metrics.totalRequests === 0) {
      return 0;
    }
    return (this.metrics.cacheHits / this.metrics.totalRequests) * 100;
  }

  /**
   * Get error rate as a percentage
   */
  getErrorRate(): number {
    if (this.metrics.totalRequests === 0) {
      return 0;
    }
    return (this.metrics.errorCount / this.metrics.totalRequests) * 100;
  }

  /**
   * Reset all metrics (useful for testing or periodic resets)
   */
  reset(): void {
    this.metrics = {
      totalRequests: 0,
      cacheHits: 0,
      errorCount: 0,
      avgResponseTime: 0,
      lastReset: Date.now(),
    };

    if (process.env.NODE_ENV === 'development') {
      console.log('Analytics metrics reset');
    }
  }

  /**
   * Get uptime since last reset in milliseconds
   */
  getUptime(): number {
    return Date.now() - this.metrics.lastReset;
  }

  /**
   * Get formatted uptime string
   */
  getFormattedUptime(): string {
    const uptime = this.getUptime();
    const hours = Math.floor(uptime / (1000 * 60 * 60));
    const minutes = Math.floor((uptime % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((uptime % (1000 * 60)) / 1000);

    return `${hours}h ${minutes}m ${seconds}s`;
  }

  /**
   * Log development statistics for debugging
   */
  private logDevelopmentStats(): void {
    if (process.env.NODE_ENV !== 'development') {
      return;
    }

    console.log('🤖 Chatbot Analytics Stats:');
    console.log(`  📊 Total Requests: ${this.metrics.totalRequests}`);
    console.log(
      `  ⚡ Cache Hits: ${this.metrics.cacheHits} (${this.getCacheHitRate().toFixed(1)}%)`
    );
    console.log(
      `  ❌ Errors: ${this.metrics.errorCount} (${this.getErrorRate().toFixed(1)}%)`
    );
    console.log(
      `  ⏱️  Avg Response Time: ${this.metrics.avgResponseTime.toFixed(0)}ms`
    );
    console.log(`  🕐 Uptime: ${this.getFormattedUptime()}`);
    console.log('─'.repeat(50));
  }

  /**
   * Get comprehensive analytics summary
   */
  getSummary(): {
    metrics: AnalyticsMetrics;
    rates: {
      cacheHitRate: number;
      errorRate: number;
    };
    uptime: {
      milliseconds: number;
      formatted: string;
    };
  } {
    return {
      metrics: this.getMetrics(),
      rates: {
        cacheHitRate: this.getCacheHitRate(),
        errorRate: this.getErrorRate(),
      },
      uptime: {
        milliseconds: this.getUptime(),
        formatted: this.getFormattedUptime(),
      },
    };
  }
}

// Create singleton analytics tracker instance
export const analyticsTracker = new ChatbotAnalyticsTracker();

/**
 * Track a request with optional response time measurement
 */
export function trackRequest(responseTime?: number): void {
  analyticsTracker.track('request', responseTime);
}

/**
 * Track a cache hit
 */
export function trackCacheHit(): void {
  analyticsTracker.track('cache_hit');
}

/**
 * Track an error occurrence
 */
export function trackError(): void {
  analyticsTracker.track('error');
}

/**
 * Get current analytics metrics
 */
export function getAnalyticsMetrics(): Readonly<AnalyticsMetrics> {
  return analyticsTracker.getMetrics();
}

/**
 * Get analytics summary with rates and uptime
 */
export function getAnalyticsSummary() {
  return analyticsTracker.getSummary();
}

/**
 * Reset analytics metrics
 */
export function resetAnalytics(): void {
  analyticsTracker.reset();
}

/**
 * Performance measurement utility
 */
export class PerformanceTimer {
  private startTime: number;

  constructor() {
    this.startTime = Date.now();
  }

  /**
   * Get elapsed time since timer creation
   */
  getElapsed(): number {
    return Date.now() - this.startTime;
  }

  /**
   * Reset the timer
   */
  reset(): void {
    this.startTime = Date.now();
  }

  /**
   * Get elapsed time and track it as a request
   */
  trackAndGetElapsed(): number {
    const elapsed = this.getElapsed();
    trackRequest(elapsed);
    return elapsed;
  }
}

/**
 * Create a new performance timer
 */
export function createPerformanceTimer(): PerformanceTimer {
  return new PerformanceTimer();
}
