import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth/auth-options';
import { LoginClient } from './login-client';
import { ToastHandler } from './toast-handler';
import { AdminUrlParamHandler } from './url-param-handler';

// Force dynamic rendering to prevent DYNAMIC_SERVER_USAGE errors
export const dynamic = 'force-dynamic';

export default async function AdminLoginPage() {
  const session = await getServerSession(authOptions);

  if (session) {
    redirect('/admin/dashboard');
  }

  const cookieStore = await cookies();
  const toastCookie = cookieStore.get('auth_toast');

  let initialToast = null;

  if (toastCookie) {
    try {
      initialToast = JSON.parse(toastCookie.value);
    } catch (_error) {}
  }

  return (
    <>
      <ToastHandler toast={initialToast} />
      <AdminUrlParamHandler />
      <LoginClient initialToast={initialToast} />
    </>
  );
}
