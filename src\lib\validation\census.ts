import { getTranslations } from 'next-intl/server';
import { z } from 'zod/v4';

/**
 * Server-side validation schemas for census operations
 * These schemas now support translations using next-intl's errorMap pattern
 * For client-side validation with translations, use src/lib/validation/client/census-client.ts
 */

// Base schema without error messages
const _baseDeleteAccountVerificationSchema = z.object({
  confirmationPhrase: z
    .string()
    .min(1)
    .refine((value) => value === 'DELETE NOW'),
});

/**
 * Create delete account verification schema with translations
 */
export async function createDeleteAccountVerificationSchema(
  locale: 'en' | 'zh-CN' = 'en'
) {
  const t = await getTranslations({ locale, namespace: 'validation' });

  return z.object({
    confirmationPhrase: z
      .string()
      .min(1, { error: t('confirmationPhraseRequired') })
      .refine((value) => value === 'DELETE NOW', {
        error: t('invalidConfirmationPhrase'),
      }),
  });
}

// Type exports for server-side validation
export type ServerDeleteAccountVerificationFormValues = z.infer<
  Awaited<ReturnType<typeof createDeleteAccountVerificationSchema>>
>;
