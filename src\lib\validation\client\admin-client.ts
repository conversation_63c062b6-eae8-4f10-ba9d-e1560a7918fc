import { z } from 'zod/v4';
import { getTodayInSydney, startOfDay } from '@/lib/utils/date-time';

/**
 * Client-side validation schemas for admin portal forms
 * These schemas use translation functions for user-facing error messages
 * Use with useTranslations('validation') hook in client components
 */

/**
 * Create admin household edit form validation schema with translations
 * @param t - Translation function from useTranslations('validation')
 */
export function createClientAdminHouseholdEditSchema(t: any) {
  return z.object({
    suburb: z.string().min(1, { error: t('suburbRequired') }),
    headFirstName: z.string().min(1, { error: t('firstNameRequired') }),
    headLastName: z.string().min(1, { error: t('lastNameRequired') }),
    headDateOfBirth: z.coerce
      .date({
        error: t('dateOfBirthRequired'),
      })
      .refine(
        (date) => {
          const today = getTodayInSydney();
          const dateToCheck = startOfDay(date);
          return dateToCheck <= today;
        },
        {
          error: t('futureDateNotAllowed'),
        }
      ),
    mobilePhone: z
      .string()
      .min(10, { error: t('phoneMinLength') })
      .max(10, { error: t('phoneMaxLength') })
      .regex(/^04\d{8}$/, { error: t('phoneNumbers') }),
    gender: z.enum(['male', 'female', 'other'], {
      error: t('selectGender'),
    }),
    headHobby: z
      .string()
      .max(100, { error: t('hobbyTooLong') })
      .optional()
      .or(z.literal('')),
    headOccupation: z
      .string()
      .max(100, { error: t('occupationTooLong') })
      .optional()
      .or(z.literal('')),
    householdComment: z
      .string()
      .max(1000, { error: t('householdCommentTooLong') })
      .optional()
      .or(z.literal('')),
  });
}

// Type exports for client-side forms
export type ClientAdminHouseholdEditFormValues = z.infer<
  ReturnType<typeof createClientAdminHouseholdEditSchema>
>;
